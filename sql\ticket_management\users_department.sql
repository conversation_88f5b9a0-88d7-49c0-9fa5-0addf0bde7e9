-- --------------------------------------------------------
-- Host:                         ************
-- Server version:               10.5.27-MariaDB - MariaDB Server
-- Server OS:                    Linux
-- HeidiSQL Version:             12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREI<PERSON><PERSON>_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- Dumping structure for table ticket_management.users_department
CREATE TABLE IF NOT EXISTS `users_department` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` tinytext NOT NULL,
  `create_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `name` (`name`(255))
) ENGINE=InnoDB AUTO_INCREMENT=64 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Dumping data for table ticket_management.users_department: ~51 rows (approximately)
INSERT IGNORE INTO `users_department` (`id`, `name`, `create_at`) VALUES
	(1, 'A/V & UCC', '2025-08-06 03:32:54'),
	(2, 'Account Manager', '2025-08-06 03:32:54'),
	(3, 'Accounting', '2025-08-06 03:32:54'),
	(4, 'Accounting & Finance', '2025-08-06 03:32:54'),
	(5, 'Accounting & Financial ', '2025-08-06 03:32:54'),
	(6, 'Advisor', '2025-08-06 03:32:54'),
	(7, 'Business Solution', '2025-08-06 03:32:54'),
	(8, 'Businiess Solution and Data Service ', '2025-08-06 03:32:54'),
	(9, 'CEO\'s Office', '2025-08-06 03:32:54'),
	(10, 'Channel', '2025-08-06 03:32:54'),
	(11, 'Cloud Computing', '2025-08-06 03:32:54'),
	(12, 'Communication', '2025-08-06 03:32:54'),
	(13, 'Corporate Affair & Administration', '2025-08-06 03:32:54'),
	(14, 'Corporate Affair & Administrative', '2025-08-06 03:32:54'),
	(15, 'Costing', '2025-08-06 03:32:54'),
	(16, 'Customer Support', '2025-08-06 03:32:54'),
	(17, 'Cybersecurity', '2025-08-06 03:32:54'),
	(18, 'Data & Security', '2025-08-06 03:32:54'),
	(19, 'Data Services & Business Solutions', '2025-08-06 03:32:54'),
	(20, 'Finance', '2025-08-06 03:32:54'),
	(21, 'Government & Public Sector ', '2025-08-06 03:32:54'),
	(22, 'Government SI & Solution Business Unit', '2025-08-06 03:32:54'),
	(23, 'Health and Safety', '2025-08-06 03:32:54'),
	(24, 'Human Capital', '2025-08-06 03:32:54'),
	(25, 'Implementation', '2025-08-06 03:32:54'),
	(26, 'Information Technology', '2025-08-06 03:32:54'),
	(27, 'Infrastructure', '2025-08-06 03:32:54'),
	(28, 'Inside Sales ', '2025-08-06 03:32:54'),
	(29, 'Internet & Connectivity', '2025-08-06 03:32:54'),
	(30, 'Inventory & Fix Asset', '2025-08-06 03:32:54'),
	(31, 'Management', '2025-08-06 03:32:54'),
	(32, 'Marcom', '2025-08-06 03:32:54'),
	(33, 'Marketing communication', '2025-08-06 03:32:54'),
	(34, 'Modern Telecom & UCC', '2025-08-06 03:32:54'),
	(35, 'Modern Workplace & Audio Visual', '2025-08-06 03:32:54'),
	(36, 'Modernworkplace', '2025-08-06 03:32:54'),
	(37, 'Mordern Workplace & Audio Visual ', '2025-08-06 03:32:54'),
	(38, 'Partner Distribution', '2025-08-06 03:32:54'),
	(39, 'Private Sector', '2025-08-06 03:32:54'),
	(40, 'Product ', '2025-08-06 03:32:54'),
	(41, 'Product & Solution', '2025-08-06 03:32:54'),
	(42, 'Product Development', '2025-08-06 03:32:54'),
	(43, 'Product Partner (Business Development)', '2025-08-06 03:32:54'),
	(44, 'Project Management', '2025-08-06 03:32:54'),
	(45, 'Purchasing', '2025-08-06 03:32:54'),
	(46, 'Sales', '2025-08-06 03:32:54'),
	(47, 'Special Project Support', '2025-08-06 03:32:54'),
	(48, 'Top Management', '2025-08-06 03:32:54'),
	(49, 'Wholesale International Business', '2025-08-06 03:32:54'),
	(50, 'Zoom & Cloud Services', '2025-08-06 03:32:54'),
	(51, 'Zoom & Modern Works', '2025-08-06 03:32:54');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;

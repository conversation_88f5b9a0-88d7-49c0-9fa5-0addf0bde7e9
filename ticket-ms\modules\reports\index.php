<?php
session_start();

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

$where_conditions = [];
$params = [];

// Date range filter
if (!empty($_GET['date_from'])) {
    $where_conditions[] = "DATE(t.created_at) >= ?";
    $params[] = $_GET['date_from'];
}
if (!empty($_GET['date_to'])) {
    $where_conditions[] = "DATE(t.created_at) <= ?";
    $params[] = $_GET['date_to'];
}
// Status filter
if (!empty($_GET['status'])) {
    $where_conditions[] = "t.status = ?";
    $params[] = $_GET['status'];
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = " WHERE " . implode(" AND ", $where_conditions);
}

// Fetch tickets from the database
$query_cal_working_time = "SELECT tk.*, c.CusName AS customer_name
FROM
  (SELECT *,
  (SELECT SUM(TIMESTAMPDIFF(SECOND, tsh1.changed_at, COALESCE(tsh2.changed_at, CURRENT_TIMESTAMP)))
      FROM ticket_status_history tsh1
      LEFT JOIN ticket_status_history tsh2 ON tsh1.ticket_number = tsh2.ticket_number
      AND tsh2.id =
        (SELECT MIN(id)
         FROM ticket_status_history
         WHERE ticket_number = tsh1.ticket_number
           AND id > tsh1.id )
      WHERE tsh1.ticket_number = t.ticket_number
        AND tsh1.status = 'Pending' ) AS pending_seconds
   FROM tickets t
   $where_clause
   LIMIT 10000) tk
LEFT JOIN KCS_DB.Customers c ON tk.customer_number = c.CusCode
";

$query = "
 SELECT `pj`.`Project_Name` AS `project_name`,
       `pt`.`Connection_Type` AS `producttype`,
       `m`.Login AS `login_name`,
       `c`.`CusName` AS `customer_name`,
       `m`.Site_Address AS `site_address`,
       tk.*
FROM ( SELECT `t`.`ticket_number` AS `ticket_number`,
      `t`.`customer_number` AS `customer_number`,
      `t`.`csno` AS `csno`,
      `t`.`issue_details` AS `issue_details`,
      `t`.`priority` AS `priority`,
      `t`.`status` AS `status`,
      `t`.`assigned_worker` AS assigned_staff,
      `t`.`created_at` AS `created_at`,
      `t`.`updated_at` AS `updated_at`,
      `t`.`open_at` AS `open_at`,
      `t`.`closed_at` AS `closed_at`,
      `t`.`estimated_stop_end` AS `estimated_stop_clock_end`,
      `t`.sub_status AS `status_detail`,
      `t`.`updated_by` AS `updated_by`,
      `tt`.`name` AS `ticket_type`,
      `af`.`name` AS `affecting_service`,
      `t`.`symptoms_details` AS `symptoms_details`,
      `t`.`product_type` AS `product_type`,
      `sv`.`name` AS `severity`,
      `ch`.`name` AS `channel_type`,
      `ti`.`login_temp` AS `login_temp`,
      `ti`.`sim_no` AS `sim_no`,
      `ti`.`sim_serial` AS `sim_serial`,
      `ti`.`sim_operator` AS `sim_operator`,
      `ti`.`sim_package` AS `sim_package`,
      `ct`.`name` AS `channel_name`,
      `tp`.main_cause,
      `tp`.sub_cause,
      `tp`.issue_cause,
      `tp`.group_cause,
      `tp`.cause_name,
      `tp`.cause_name_en,
      `u`.`username` AS `created_by`,
      (SELECT SUM(TIMESTAMPDIFF(SECOND, tsh1.changed_at, COALESCE(tsh2.changed_at, CURRENT_TIMESTAMP)))
      FROM ticket_status_history tsh1
      LEFT JOIN ticket_status_history tsh2 ON tsh1.ticket_number = tsh2.ticket_number
      AND tsh2.id =
        (SELECT MIN(id)
         FROM ticket_status_history
         WHERE ticket_number = tsh1.ticket_number
           AND id > tsh1.id )
      WHERE tsh1.ticket_number = t.ticket_number
        AND tsh1.status = 'Pending' ) AS pending_seconds
      FROM `ticket_management`.`tickets` `t`
      LEFT JOIN `ticket_management`.`ticket_extended` `te` ON(`t`.`ticket_number` = `te`.`ticket_number`)
      LEFT JOIN `ticket_management`.`ticket_interim` `ti` ON(`t`.`ticket_number` = `ti`.`ticket_number`)
      LEFT JOIN `ticket_management`.`users` `u` ON(`t`.`username` = `u`.`username`)
      LEFT JOIN `ticket_management`.`channel_types` `ct` ON(`t`.`channel_type` = `ct`.`id`)
      LEFT JOIN `ticket_management`.`ticket_cause_template` `tp` ON(`t`.`cause_detail_template_id` = `tp`.`id`)
      LEFT JOIN `ticket_management`.`ticket_type` `tt` ON(`t`.`ticket_type` = `tt`.`id`)
      LEFT JOIN `ticket_management`.`affecting_service_types` `af` ON(`t`.`affecting_service` = `af`.`id`)
      LEFT JOIN `ticket_management`.`severity_types` `sv` ON(`t`.`severity` = `sv`.`code`)
      LEFT JOIN `ticket_management`.`channel_types` `ch` ON(`t`.`channel_type` = `ch`.`id`)
      $where_clause
      LIMIT 1000) AS tk
LEFT JOIN `KCS_DB`.`Main` `m` ON(`tk`.`csno` = `m`.Ref)
LEFT JOIN `KCS_DB`.`Connection_Type` `pt` ON(`m`.`Connection_Type` = `pt`.`Connection_ID`)
LEFT JOIN `KCS_DB`.`Customers` `c` ON(`tk`.`customer_number` = `c`.`CusCode`)
LEFT JOIN `KCS_DB`.`Project` `pj` ON (`m`.`Project_ID` = `pj`.`Project_ID`)
";

//var_dump($query_cal_working_time);
//$result = $pdo->query($query_cal_working_time);
$results = [];
if ($_GET['action'] ?? '' == 'filter') {
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
}
function get_stop_clock_event($ticket_number)
{
    global $pdo;
    $query = "SELECT CONCAT(tsh1.changed_at, ' - ', IFNULL(tsh2.changed_at, 'Null'), ', ', tsh1.sub_status, ', ', tsh1.comments)AS `stop_clock_event`
    FROM ticket_status_history tsh1
    LEFT JOIN ticket_status_history tsh2 ON tsh1.ticket_number = tsh2.ticket_number
    AND tsh2.id =
    (SELECT MIN(id)
    FROM ticket_status_history
    WHERE ticket_number = tsh1.ticket_number
        AND id > tsh1.id )
    WHERE tsh1.ticket_number = ?
    AND tsh1.status = 'Pending'";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$ticket_number]);
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $stop_clock_event = '';
    foreach ($rows as $index => $row) {
        $stop_clock_event .= '(' . ($index + 1) . ') ' . $row['stop_clock_event'] . '<br>';
    }
    return $stop_clock_event;
}

require_once '../../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="card shadow">
        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Ticket Reports
                <?php if (!empty($results)): ?>
                    <span class="badge badge-light"><?php echo number_format(count($results)); ?></span>
                <?php endif; ?>
            </h5>
            <div>
                <button class="btn btn-outline-light mr-2" id="showHelpBtn">
                    <i class="fas fa-question-circle"></i> Help
                </button>
                <?php 
                // Check if user is an admin to show export button
                if ($_SESSION['userdata']['role'] === 'admin'): ?>
                    <button class="btn btn-outline-light" onclick="exportToExcel('ticketReport')">
                        <i class="fas fa-download"></i> Export to Excel
                    </button>
                <?php endif; ?>

            </div>
        </div>
        <div class="card-body">
            <!-- Filter Form -->
            <div class="card mb-3">
                <div class="card-body">
                    <form id="ticketFilterForm" method="GET" class="row align-items-end">
                        <input type="hidden" name="action" value="filter">
                        <div class="col-md-3">
                            <label for="date_from">Date From:</label>
                            <input type="date" id="date_from" name="date_from"
                                class="form-control"
                                value="<?php echo $_GET['date_from'] ?? ''; ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="date_to">Date To:</label>
                            <input type="date" id="date_to" name="date_to"
                                class="form-control"
                                value="<?php echo $_GET['date_to'] ?? ''; ?>">
                        </div>
                        <div class="col-md-3">
                            <label for="status">Status:</label>
                            <select id="status" name="status" class="form-control">
                                <option value="">All Status</option>
                                <option value="Open" <?php echo ($_GET['status'] ?? '') === 'Open' ? 'selected' : ''; ?>>Open</option>
                                <option value="In Progress" <?php echo ($_GET['status'] ?? '') === 'In Progress' ? 'selected' : ''; ?>>In Progress</option>
                                <option value="Pending" <?php echo ($_GET['status'] ?? '') === 'Pending' ? 'selected' : ''; ?>>On Hold(Stop Clock)</option>
                                <option value="Closed" <?php echo ($_GET['status'] ?? '') === 'Closed' ? 'selected' : ''; ?>>Closed</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter"></i> Apply Filters
                            </button>
                            <a href="index.php" class="btn btn-secondary">
                                <i class="fas fa-undo"></i> Reset
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <?php

            if (!empty($results)): ?>
                <div class="small">
                    <table class="table" id="ticketReport">
                        <thead>
                            <tr>
                                <th>Ticket #</th>
                                <th>Customer</th>
                                <th>CS No.(Ref)</th>
                                <th>Project Name</th>
                                <th>Login</th>
                                <th>Produc Type</th>
                                <th width="500px">Issue Details</th>
                                <th>Status</th>
                                <th>Assigned Staff</th>
                                <th>Created At</th>
                                <th>Updated At</th>
                                <th>Open At</th>
                                <th>Closed At</th>
                                <th>Estimated Stop Clock End</th>
                                <th>Ticket Type</th>
                                <th>Affecting Service</th>
                                <th>Severity</th>
                                <th>Channel Type</th>
                                <th>Symptoms Details</th>
                                <th>SIM No.</th>
                                <th>SIM Serial</th>
                                <th>SIM Operator</th>
                                <th>Main Cause</th>
                                <th>Sub Cause</th>
                                <th>Issue Cause</th>
                                <th>Group Cause</th>
                                <th width="500px">Cause Name</th>
                                <th width="500px">Cause Name(Eng)</th>
                                <th>Created By</th>
                                <th>Down Duration(Total)</th>
                                <th>On-Hold(Stop Clock)</th>
                                <th>Down Duration exclude On-Hold</th>
                                <th>Stop Clock Event</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($results as $row):
                                $working_time_obj = calculateWorkingTime($row['open_at'], $row['closed_at'], $row['pending_seconds'] ?? 0);
                                //$working_time = calculateTicketWorkingTimeById($row['ticket_number']); 
                               
                                $working_time_obj_fomat = secondsToTimeFormat($working_time_obj['seconds']);
                                $working_time = $working_time_obj_fomat['full_hours_format'];
                                //$working_time_min = secondsToMinutes($working_time_obj['seconds']);

                                $pending_time_obj = secondsToTimeFormat($row['pending_seconds'] ?? 0);
                                $pending_time = $pending_time_obj['full_hours_format'];
                                //$pending_time_min = secondsToMinutes($pending_time_obj['seconds']);

                                $totol_seconds = $working_time_obj['seconds'] + $pending_time_obj['seconds'];
                                $total_time_obj = secondsToTimeFormat($totol_seconds);
                                $total_time = $total_time_obj['full_hours_format'];

                            ?>
                                <tr>
                                    <td><?php echo $row['ticket_number'] ?? ''; ?></td>
                                    <td><?php echo $row['customer_number'] ?? ''; ?></td>
                                    <td><?php echo $row['csno'] ?? ''; ?></td>
                                    <td><?php echo $row['project_name'] ?? ''; ?></td>
                                    <td><?php echo $row['login_name'] ?? ''; ?></td>
                                    <td><?php echo $row['producttype'] ?? ''; ?></td>
                                    <td><?php echo $row['issue_details'] ?? ''; ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo getStatusBadgeClass($row['status']); ?>">
                                            <?php
                                            $status = $row['status'] ?? '';
                                            if ($status === 'Pending') {
                                                echo 'On Hold(Stop Clock)';
                                            } else {
                                                echo $status;
                                            }

                                            ?>
                                        </span>
                                    </td>
                                    <td><?php echo $row['assigned_staff'] ?? '-'; ?></td>
                                    <td><?php echo $row['created_at']; ?></td>
                                    <td><?php echo $row['updated_at']; ?></td>
                                    <td><?php echo $row['open_at']; ?></td>
                                    <td><?php echo $row['closed_at']; ?></td>
                                    <td><?php echo $row['estimated_stop_clock_end']; ?></td>
                                    <td><?php echo $row['ticket_type'] ?? ''; ?></td>
                                    <td><?php echo $row['affecting_service'] ?? ''; ?></td>
                                    <td><?php echo $row['severity'] ?? ''; ?></td>
                                    <td><?php echo $row['channel_type'] ?? ''; ?></td>
                                    <td><?php echo $row['symptoms_details'] ?? ''; ?></td>
                                    <td><?php echo $row['sim_no'] ?? ''; ?></td>
                                    <td><?php echo $row['sim_serial'] ?? ''; ?></td>
                                    <td><?php echo $row['sim_operator'] ?? ''; ?></td>
                                    <td><?php echo $row['main_cause'] ?? ''; ?></td>
                                    <td><?php echo $row['sub_cause'] ?? ''; ?></td>
                                    <td><?php echo $row['issue_cause'] ?? ''; ?></td>
                                    <td><?php echo $row['group_cause'] ?? ''; ?></td>
                                    <td><?php echo $row['cause_name'] ?? ''; ?></td>
                                    <td><?php echo $row['cause_name_en'] ?? ''; ?></td>
                                    <td><?php echo $row['created_by'] ?? ''; ?></td>
                                    <td><?php echo $total_time; ?></td>
                                    <td><?php echo $pending_time; ?></td>
                                    <td><?php echo $working_time; ?></td>
                                    <td>
                                        <?php echo get_stop_clock_event($row['ticket_number']); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="alert alert-info">No Data.</div>
            <?php endif; ?>
        </div>
    </div>
</div>

<div id="reportHelpNote" class="position-fixed shadow-sm" style="width: 300px; z-index: 1000; right: 20px; top: 80px;">
    <div class="card note-card">
        <div class="card-header note-header py-2" id="helpDragHandle">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    <i class="fas fa-question-circle"></i> Report Guide
                </h6>
                <div>
                    <button type="button" class="btn btn-link btn-sm text-secondary p-0 mr-2" id="minimizeHelp">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-link btn-sm text-secondary p-0" id="closeHelp">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body note-body py-2" id="helpContent">
            <div class="help-item mb-2">
                <strong><i class="fas fa-clock text-info"></i> Working Time:</strong>
                <ul class="small pl-4 mb-2">
                    <li>เริ่มเวลาตั้งแต่สร้าง Ticket</li>
                    <li>เมื่อ Status = Closed</li>
                    <li>ไม่นับเวลาช่วง Status = Pending</li>
                </ul>
            </div>
            <div class="help-item mb-2">
                <strong><i class="fas fa-tags text-warning"></i> Status Types:</strong>
                <ul class="small pl-4 mb-2">
                    <li>Open - เริ่มสร้าง Ticket</li>
                    <li>In Progress - กำลังดำเนินการ</li>
                    <li>Pending - รอข้อมูลเพิ่มเติม</li>
                    <li>Closed - เสร็จสิ้น</li>
                </ul>
            </div>
            <div class="help-item">
                <strong><i class="fas fa-download text-success"></i> Export Tips:</strong>
                <ul class="small pl-4 mb-0">
                    <li>ปุ่ม Export สำหรับการดาวน์โหลด Excel</li>
                    <li>การ Export ข้อมูลจะรวมทั้งหมด</li>
                    <li>สามารถกรองข้อมูลก่อน Export ได้</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?php
require_once '../../includes/main_script_loader.php';
?>
<script>
    function exportToExcel(tableID) {
        let table = document.getElementById(tableID);
        if (!table) {
            alert('Table not found!');
            return;
        }
        let html = table.outerHTML;
        let url = 'data:application/vnd.ms-excel,' + encodeURIComponent(html);
        let downloadLink = document.createElement("a");
        document.body.appendChild(downloadLink);
        downloadLink.href = url;
        downloadLink.download = 'ticket_report.xls';
        downloadLink.click();
        document.body.removeChild(downloadLink);
    }
    $(document).ready(function() {
        // Set default dates if not set
        if (!$('#date_from').val()) {
            let defaultFromDate = new Date();
            defaultFromDate.setDate(defaultFromDate.getDate() - 30); // Last 30 days
            $('#date_from').val(defaultFromDate.toISOString().split('T')[0]);
        }

        if (!$('#date_to').val()) {
            let today = new Date();
            $('#date_to').val(today.toISOString().split('T')[0]);
        }

        // Validate date range
        $('#ticketFilterForm').submit(function(e) {
            const dateFrom = new Date($('#date_from').val());
            const dateTo = new Date($('#date_to').val());

            if (dateFrom > dateTo) {
                e.preventDefault();
                alert('Date From cannot be later than Date To');
                return false;
            }

            // Disable form and show loading
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.html();

            submitBtn.prop('disabled', true);
            submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Loading...');

        });

        // Update date_to min value when date_from changes
        $('#date_from').change(function() {
            $('#date_to').attr('min', $(this).val());
        });

        // Update date_from max value when date_to changes
        $('#date_to').change(function() {
            $('#date_from').attr('max', $(this).val());
        });
    });
</script>

<script src="<?= BASE_URL ?>/assets/js/reports-ticket.js?v=<?= $timestamp ?>"></script>
<?php
require_once '../../includes/footer.php';
?>
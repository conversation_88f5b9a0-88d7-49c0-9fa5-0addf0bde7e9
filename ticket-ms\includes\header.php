<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

$OS_PATH_WEB = '/var/www/html/cs.1-to-all.com/app/ticket-ms';
require_once  $OS_PATH_WEB . '/config/defined.conf.php';
$timestamp = time();

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= WEBTITLE ?></title>
    <link rel="icon" href="<?= BASE_URL ?>/assets/images/favicon.png" sizes="32x32">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.css">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap4-theme@1.0.0/dist/select2-bootstrap4.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet"/>

    <link href="<?= BASE_URL ?>/assets/css/style.css?v=<?= $timestamp ?>" rel="stylesheet">
    <link href="<?= BASE_URL ?>/assets/css/custom.css?v=<?= $timestamp ?>" rel="stylesheet">

</head>
<body>
    <style>
        .navbar {
            background-color: #0056B3;
        }
        .navbar-brand {
            color: #FFFFFF !important;
        }

        .navbar-brand img {
            height: 40px;
            margin-right: 10px;
        }

        .badge-overtime {
            background-color: #e88307ff !important;
            color: white !important;
        }

        .overdue {
            cursor: pointer;
            animation: blink 1s linear infinite;
        }

        .interim {
            cursor: pointer;
            animation: blink 1s linear infinite;
        }

        @keyframes blink {
            50% {
                opacity: 0.5;
            }
        }
    </style>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <a class="navbar-brand" href="<?= BASE_URL ?>/">
            <img src="<?= BASE_URL ?>/assets/images/favicon.png" alt="Company Logo">
            CS Ticket Management
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarContent">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarContent">
            <form class="form-inline my-2 my-lg-0" action="<?= BASE_URL ?>/modules/tickets/search.php" method="GET">
                <input class="form-control mr-sm-2" type="search" name="ticket" placeholder="Search ticket..." aria-label="Search" title="Ticket No. | Customer Name | Customer No. | CS No.(Ref)" value="<?= $_GET['ticket'] ?? '' ?>" required>
                <button class="btn btn-outline-light my-2 my-sm-0" type="submit">Search</button>

                <input type="checkbox" name="search_login" value="1" class="ml-2" id="search_login" <?php echo (($_GET['search_login'] ?? '') == '1') ? 'checked' : ''; ?>>
                <label class="ml-2 text-white" for="search_login">Login</label>
            </form>
            <ul class="navbar-nav ml-auto">
                <li class="nav-item"><a class="nav-link" href="<?= BASE_URL ?>/index.php">Home</a></li>
                <li class="nav-item"><a class="nav-link" href="<?= BASE_URL ?>/modules/dashboard/index.php">Dashboard</a></li>
                <li class="nav-item"><a class="nav-link" href="<?= BASE_URL ?>/modules/tickets/list.php">Tickets</a></li>
                <li class="nav-item"><a class="nav-link" href="<?= BASE_URL ?>/modules/reports/index.php">Reports</a></li>
                <li class="nav-item"><a class="nav-link" href="<?= BASE_URL ?>/modules/settings/index.php">Settings</a></li>
                <?php if (isset($_SESSION['userdata'])): ?>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-toggle="dropdown">
                            <i class="fas fa-user-circle"></i>
                            <?php echo htmlspecialchars($_SESSION['userdata']['username']); ?>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <span class="dropdown-item-text">
                                <strong>Role:</strong> <?php echo htmlspecialchars($_SESSION['userdata']['role']); ?>
                            </span>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="<?= BASE_URL ?>/modules/auth/logout.php">
                                <i class="fas fa-sign-out-alt"></i> Sign Out
                            </a>
                        </div>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </nav>
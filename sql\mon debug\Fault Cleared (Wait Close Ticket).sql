WITH	LatestEvent AS (
		SELECT	d.device_id,d.hostname,	MAX(e.datetime) AS max_datetime
		FROM
			devices d	INNER JOIN eventlog e ON d.device_id = e.device_id
		WHERE
			d.status = 1 AND e.message LIKE '%Up from icmp check%'
		GROUP BY
			d.hostname
	)
	
SELECT	d.device_id,d.hostname,	d.sysName,	d.display,d.status,d.purpose,
	d.hardware,	e.datetime,	e.message,dg.desc,d.Mon_timeid,d.Mon_timestart,
	d.Mon_timeend,	'' AS `Owner`
FROM
	devices d	INNER JOIN eventlog e ON d.device_id = e.device_id
	INNER JOIN device_groups dg ON d.purpose = dg.name
	INNER JOIN LatestEvent le ON d.hostname = le.hostname	AND e.datetime = le.max_datetime
WHERE
	e.message LIKE '%Device status changed to Up from icmp check%'
	AND dg.desc IN ('D1', 'D2', 'D3', 'D4', 'D5', 'D6', 'D7'	)
	AND d.display IN ('BIPT4270781')
ORDER BY
	e.datetime DESC
<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Replace the existing SQL query with this:
$where_conditions = [];
$params = [];

// Date range filter
if (!empty($_GET['date_from'])) {
    $where_conditions[] = "DATE(tk.created_at) >= ?";
    $params[] = $_GET['date_from'];
}
if (!empty($_GET['date_to'])) {
    $where_conditions[] = "DATE(tk.created_at) <= ?";
    $params[] = $_GET['date_to'];
}

// Status filter
if (isset($_GET['status_arr']) && is_array($_GET['status_arr'])) {
    $status_arr = implode("','", array_map('trim', $_GET['status_arr']));
    if (!empty($status_arr)) {
        $where_conditions[] = "tk.status IN ('$status_arr')";
    }
    //$params[] = $status_arr;
}

$where_clause = '';
if (!empty($where_conditions)) {
    $where_clause = "WHERE " . implode(" AND ", $where_conditions);
}

$limit = 100;
if ($_GET['action'] ?? '' == 'filter') {
    $limit = 10000;
}
$sql = "SELECT t.*, cus.CusName AS customer_name,m.Site_Address,m.Login AS login_name FROM 
(
    SELECT 
    *
    FROM tickets tk
    $where_clause
    ORDER BY tk.created_at DESC limit $limit
) as t
LEFT JOIN `KCS_DB`.`Main` `m` ON(`t`.`csno` = `m`.Ref)
LEFT JOIN KCS_DB.Customers cus ON t.customer_number=cus.CusCode 
ORDER BY created_at DESC";

$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$result = $stmt;

require_once '../../includes/header.php';

// Fetch channel types from database
$channel_query = "SELECT id, name FROM channel_types WHERE is_active=1";
$channel_result = $pdo->query($channel_query);
$channel_types = [];
while ($channel = $channel_result->fetch(PDO::FETCH_ASSOC)) {
    $channel_types[$channel['id']] = $channel['name'];
}

$datatable_sort_index = 0;
if ($_SESSION['userdata']['role'] === 'admin') {
    $datatable_sort_index = 1;
}
?>
<div class="container-fluid mt-4 small">
    <!-- Filter Form -->
    <div class="card mb-3">
        <div class="card-body">
            <form id="ticketFilterForm" method="GET" class="row align-items-end">
                <input type="hidden" name="action" value="filter">
                <div class="col-md-2">
                    <label for="date_from">Date From:</label>
                    <input type="date" id="date_from" name="date_from"
                        class="form-control"
                        value="<?php echo $_GET['date_from'] ?? ''; ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_to">Date To:</label>
                    <input type="date" id="date_to" name="date_to"
                        class="form-control"
                        value="<?php echo $_GET['date_to'] ?? ''; ?>">
                </div>
                <div class="col-md-3 select2-container">
                    <label for="status">Status:</label>
                    <select id="status" name="status_arr[]" class="form-control" multiple="multiple">
                        <option value="Open" <?php echo in_array('Open', $_GET['status_arr'] ?? []) ? 'selected' : ''; ?>>Open</option>
                        <option value="In Progress" <?php echo in_array('In Progress', $_GET['status_arr'] ?? []) ? 'selected' : ''; ?>>In Progress</option>
                        <option value="Pending" <?php echo in_array('Pending', $_GET['status_arr'] ?? []) ? 'selected' : ''; ?>>On Hold(Stop Clock)</option>
                        <option value="Closed" <?php echo in_array('Closed', $_GET['status_arr'] ?? []) ? 'selected' : ''; ?>>Closed</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> Apply Filters
                    </button>
                    <a href="list.php" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> Reset
                    </a>
                </div>
            </form>
        </div>
    </div>

    <?php if (!empty($_GET['date_from']) || !empty($_GET['date_to']) || !empty($_GET['status'])): ?>
        <div class="alert alert-info mb-3">
            <strong>Active Filters:</strong>
            <?php
            if (!empty($_GET['date_from'])) {
                echo ' From: ' . date('d/m/Y', strtotime($_GET['date_from']));
            }
            if (!empty($_GET['date_to'])) {
                echo ' To: ' . date('d/m/Y', strtotime($_GET['date_to']));
            }
            if (isset($_GET['status_arr']) && is_array($_GET['status_arr'])) {
                $status_arr = implode(", ", array_map('trim', $_GET['status_arr']));
                echo ' Status: (' . htmlspecialchars($status_arr) . ')';
            }

            echo ' Found: ' . $result->rowCount() . ' tickets';
            ?>
            <a href="list.php" class="float-right">Clear All Filters</a>
        </div>
    <?php endif; ?>

    <div class="d-flex justify-content-between align-items-center mb-3">
        <div class="d-flex align-items-center">
            <h1 class="mb-0 mr-3">Ticket List</h1>
            <div class="custom-control custom-switch">
                <input type="checkbox" class="custom-control-input" id="autoRefreshToggle" checked>
                <label class="custom-control-label" for="autoRefreshToggle">Auto Refresh</label>
            </div>
        </div>
        <div>
            <?php if ($_SESSION['userdata']['role'] === 'admin'): ?>
                <button type="button" id="transferSelectedBtn" class="btn btn-info mr-2" disabled>
                    <i class="fas fa-exchange-alt"></i> Transfer Selected (<span id="selectedCount">0</span>)
                </button>
                <a href="import.php" class="btn btn-success mr-2">
                    <i class="fas fa-file-excel"></i> Import Tickets
                </a>
                <a href="import_history.php" class="btn btn-info mr-2">
                    <i class="fas fa-history"></i> Import History
                </a>
            <?php endif; ?>
            <a href="create.php" class="btn btn-primary">
                <i class="fas fa-plus"></i> Create Ticket
            </a>
        </div>
    </div>
    <table class="table table-striped table-hover" id="ticketList" name="ticketList">
        <thead>
            <tr>
                <?php if ($_SESSION['userdata']['role'] === 'admin'): ?>
                    <th>
                        <input type="checkbox" id="selectAll" title="Select All">
                    </th>
                <?php endif; ?>
                <th>Ticket No.</th>
                <th>Customer Name</th>
                <th>CS No.(Ref)</th>
                <th>Login</th>
                <th>Site Address</th>
                <th style="width: 400px;">Issue Details</th>
                <th>Assigned Staff</th>
                <th>Status</th>
                <th>Created At</th>
                <th>Open At</th>
                <th>Closed At</th>
                <th>Channel</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            <?php if ($result->rowCount() > 0): ?>
                <?php while ($row = $result->fetch(PDO::FETCH_ASSOC)): ?>
                    <tr>
                        <?php if ($_SESSION['userdata']['role'] === 'admin'): ?>
                            <td>
                                <input type="checkbox" class="ticket-checkbox"
                                    value="<?php echo $row['ticket_number']; ?>">
                            </td>
                        <?php endif; ?>
                        <td>
                            <a target="_blank" href="<?= BASE_URL ?>/modules/tickets/ticket.php?tkt_id=<?php echo urlencode($row['ticket_number'] ?? ''); ?>" title="View Ticket">
                                <?php echo $row['ticket_number']; ?></a>
                            <?php
                            if ($row['status'] !== 'Closed') {
                                $updated_time = strtotime($row['updated_at']);
                                $current_time = time();
                                $hours_diff = ($current_time - $updated_time) / 3600;
                                if ($hours_diff >= 2) {
                                    echo '<br> <span class="badge badge-danger overdue" title="No any update ' . round($hours_diff, 1) . ' hrs ago">Overdue</span>';
                                }
                            }
                            if ($row['status'] === 'Pending') {
                                if ($row['estimated_stop_end']) {
                                    $estimated_stop_end = strtotime($row['estimated_stop_end']);
                                    $current_time = time();
                                    if ($current_time >= $estimated_stop_end) {
                                        echo '<br><span class="badge badge-overtime overdue" title="Overtime">Overtime</span>';
                                    }
                                }
                            }

                            ?>
                        </td>
                        <td><?php echo $row['customer_name']; ?></td>
                        <td><?php echo $row['csno']; ?></td>
                        <td><?php echo $row['login_name'] ?? ''; ?></td>
                        <td><?php echo $row['Site_Address'] ?? ''; ?></td>
                        <td><?php echo $row['issue_details'] ?? '-'; ?></td>
                        <td><?php echo $row['assigned_worker'] ?? '-'; ?></td>
                        <td><span class="badge badge-<?php echo getStatusBadgeClass($row['status']); ?>">
                                <?php
                                $status = $row['status'] ?? '';
                                if ($status === 'Pending') {
                                    echo 'On Hold(Stop Clock)';
                                } else {
                                    echo htmlspecialchars($status);
                                }

                                ?>
                            </span>
                        </td>
                        <td><?php echo $row['created_at']??'-'; ?></td>
                        <td>
                            <?php echo $row['open_at']??'-'; ?>
                        </td>
                        <td>
                            <?php echo $row['closed_at']??'-'; ?>
                        </td>

                        <td>
                            <?php
                            echo isset($channel_types[$row['channel_type']]) ?
                                htmlspecialchars($channel_types[$row['channel_type']]) :
                                'Unknown';
                            ?>
                        </td>
                        <td style="white-space: nowrap;">
                            <a target="_blank" href="ticket.php?tkt_id=<?php echo urlencode($row['ticket_number']); ?>" class="btn btn-sm btn-info" title="View Ticket">
                                <i class="fas fa-eye"></i></a>
                            <?php if ($status !== 'Closed'): ?>
                                <a target="_blank" href="edit.php?tkt_id=<?php echo urlencode($row['ticket_number']); ?>" class="btn btn-sm btn-warning" title="Edit Ticket">
                                    <i class="fas fa-edit"></i></a>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endwhile; ?>
            <?php else: ?>
                <tr>
                    <td colspan="6">No tickets found.</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
    <?php if ($_SESSION['userdata']['role'] === 'admin'): ?>
        <div class="modal fade" id="transferModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title">Transfer Selected Tickets</h5>
                        <button type="button" class="close text-white" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <form id="transferForm">
                        <div class="modal-body">
                            <div class="form-group">
                                <label>Transfer to Staff:</label>
                                <select name="new_worker" id="newWorkerSelect" class="form-control" required>
                                    <option value="">Select Staff</option>
                                    <?php
                                    $staff_query = "SELECT * 
                                          FROM users 
                                          WHERE status = 'active' AND division='Operation'
                                          ORDER BY fullname";
                                    $staff_result = $pdo->query($staff_query);
                                    while ($staff = $staff_result->fetch()) {
                                        echo '<option value="' . htmlspecialchars($staff['username'] ?? '') . '">' .
                                            htmlspecialchars($staff['fullname'] ?? '') . '  (' . $staff['department'] . ')</option>';
                                    }
                                    ?>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Transfer Reason:</label>
                                <textarea name="transfer_reason" class="form-control" rows="3" required
                                    placeholder="Explain why you're transferring these tickets..."></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-info btn-transfer" >Transfer Tickets</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>
<?php
require_once '../../includes/main_script_loader.php';
?>
<script type="text/JavaScript">
    $(document).ready(function() {
        $('#status').select2();
        $('#ticketList').DataTable({
            "paging": true,
            "pageLength": 100,
            "order": [[<?= $datatable_sort_index ?>, "desc"]], // Sort by 
            "columnDefs": [
                {
                    // Format the date column for proper sorting
                    "targets": 7, // "Created At" column index
                    "render": function(data, type, row) {
                        // For sorting/filtering, convert to YYYY-MM-DD format
                        if (type === 'sort' || type === 'type') {
                            return data;
                        }
                        // For display, keep the original format
                        return data;
                    }
                }
            ],
            "language": {
                "search": "Search:",
                "lengthMenu": "Show _MENU_ tickets per page",
                "info": "Showing _START_ to _END_ of _TOTAL_ tickets"
            }
        });
    });
</script>
<script>
    // Store auto-refresh state in localStorage
    let autoRefreshEnabled = localStorage.getItem('autoRefreshEnabled') !== 'false';
    let timeLeft = 300; // 5 minutes in seconds
    let refreshTimer;
    let countdownTimer;

    function startTimers() {
        // Clear existing timers
        clearTimeout(refreshTimer);
        clearInterval(countdownTimer);

        if (autoRefreshEnabled) {
            // Set page refresh timer
            refreshTimer = setTimeout(function() {
                window.location.reload();
            }, 60000 * 5); // 5 minutes

            // Set countdown timer
            timeLeft = 300;
            countdownTimer = setInterval(function() {
                timeLeft--;
                if (timeLeft >= 0) {
                    document.title = `(${timeLeft}s) ` + '<?= WEBTITLE ?>';
                }
            }, 1000);
        } else {
            document.title = '<?= WEBTITLE ?>';
        }
    }

    $(document).ready(function() {
        // Initialize toggle state
        $('#autoRefreshToggle').prop('checked', autoRefreshEnabled);
        startTimers();

        // Handle toggle changes
        $('#autoRefreshToggle').change(function() {
            autoRefreshEnabled = this.checked;
            localStorage.setItem('autoRefreshEnabled', autoRefreshEnabled);
            startTimers();

            // Show feedback toast
            const message = autoRefreshEnabled ?
                'Auto refresh enabled (5 minutes)' :
                'Auto refresh disabled';

            // Create and show toast
            const toast = $(`
            <div class="position-fixed bottom-0 right-0 p-3" style="z-index: 5000; right: 0; bottom: 0;">
                <div class="toast" role="alert">
                    <div class="toast-body bg-${autoRefreshEnabled ? 'success' : 'secondary'} text-white">
                        ${message}
                    </div>
                </div>
            </div>
        `);

            $('body').append(toast);
            $('.toast').toast({
                delay: 2000,
                animation: true
            }).toast('show');

            // Remove toast after it's hidden
            $('.toast').on('hidden.bs.toast', function() {
                $(this).parent().remove();
            });
        });
    });
</script>
<script>
    $(document).ready(function() {
        // Set default dates if not set
        if (!$('#date_from').val()) {
            let defaultFromDate = new Date();
            defaultFromDate.setDate(defaultFromDate.getDate() - 30); // Last 30 days
            $('#date_from').val(defaultFromDate.toISOString().split('T')[0]);
        }

        if (!$('#date_to').val()) {
            let today = new Date();
            $('#date_to').val(today.toISOString().split('T')[0]);
        }

        // Validate date range
        $('#ticketFilterForm').submit(function(e) {
            const dateFrom = new Date($('#date_from').val());
            const dateTo = new Date($('#date_to').val());

            if (dateFrom > dateTo) {
                e.preventDefault();
                toastr.warning('Date From cannot be later than Date To');
                return false;
            }
            // Disable form and show loading
            const submitBtn = $(this).find('button[type="submit"]');
            const originalText = submitBtn.html();

            submitBtn.prop('disabled', true);
            submitBtn.html('<i class="fas fa-spinner fa-spin"></i> Loading...');
        });

        // Update date_to min value when date_from changes
        $('#date_from').change(function() {
            $('#date_to').attr('min', $(this).val());
        });

        // Update date_from max value when date_to changes
        $('#date_to').change(function() {
            $('#date_from').attr('max', $(this).val());
        });
    });
</script>

<script>
    $(document).ready(function() {
        // Initialize Select2 for transfer modal dropdown
        $('#newWorkerSelect').select2({
            theme: 'bootstrap4',
            placeholder: 'Search and select staff...',
            allowClear: true,
            width: '100%',
            dropdownParent: $('#transferModal')
        });

        const selectAll = $('#selectAll');
        const ticketCheckboxes = $('.ticket-checkbox');
        const transferBtn = $('#transferSelectedBtn');
        const selectedCountSpan = $('#selectedCount');

        // Handle "Select All" checkbox
        selectAll.change(function() {
            ticketCheckboxes.prop('checked', this.checked);
            updateTransferButton();
        });

        // Handle individual checkboxes
        ticketCheckboxes.change(function() {
            updateTransferButton();
            // Update "Select All" if needed
            selectAll.prop('checked',
                ticketCheckboxes.length === ticketCheckboxes.filter(':checked').length);
        });

        // Update transfer button state
        function updateTransferButton() {
            const selectedCount = ticketCheckboxes.filter(':checked').length;
            transferBtn.prop('disabled', selectedCount === 0);
            selectedCountSpan.text(selectedCount);
        }

        // Handle transfer button click
        transferBtn.click(function() {
            $('#transferModal').modal('show');
        });

        // Handle transfer form submission
        $('#transferForm').submit(function(e) {
            e.preventDefault();
            const btntraffer = $('.btn-transfer');
            const originalText = btntraffer.html();
            btntraffer.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Transferring...');
            const selectedTickets = [];
            ticketCheckboxes.filter(':checked').each(function() {
                selectedTickets.push($(this).val());
            });

            $.ajax({
                url: 'transfer_tickets.php',
                method: 'POST',
                data: {
                    ticket_numbers: JSON.stringify(selectedTickets),
                    new_worker: $('select[name="new_worker"]').val(),
                    transfer_reason: $('textarea[name="transfer_reason"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success('Tickets transferred successfully!');
                        setTimeout(() => { location.reload();}, 3000);
                    } else {
                        toastr.error('Error: ' + response.message);
                         btntraffer.prop('disabled', false).html(originalText);
                    }
                },
                error: function() {
                    toastr.error('Error occurred during transfer');
                     btntraffer.prop('disabled', false).html(originalText);
                }
            });
        });
    });
</script>
<?php
require_once '../../includes/footer.php';
?>
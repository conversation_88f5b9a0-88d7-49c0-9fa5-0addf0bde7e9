SELECT 
    `status`,
    SUM(CASE 
        WHEN created_at >= DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') THEN 1 
        ELSE 0 
    END) as current_month,
    SUM(CASE 
        WHEN created_at >= DATE_SUB(DATE_FORMAT(CURRENT_DATE, '%Y-%m-01'), INTERVAL 1 MONTH)
        AND created_at < DATE_FORMAT(CURRENT_DATE, '%Y-%m-01') THEN 1 
        ELSE 0 
    END) as last_month,
    SUM(CASE 
        WHEN created_at >= DATE_SUB(DATE_FORMAT(CURRENT_DATE, '%Y-%m-01'), INTERVAL 2 MONTH)
        AND created_at < DATE_SUB(DATE_FORMAT(CURRENT_DATE, '%Y-%m-01'), INTERVAL 1 MONTH) THEN 1 
        ELSE 0 
    END) as two_months_ago,
    SUM(CASE 
        WHEN created_at < DATE_SUB(DATE_FORMAT(CURRENT_DATE, '%Y-%m-01'), INTERVAL 2 MONTH) THEN 1 
        ELSE 0 
    END) as older_tickets,
    COUNT(*) as total_tickets
FROM 
    tickets
WHERE
	`status`<>'Closed'
GROUP BY 
    `status`

ORDER BY 
    FIELD(`status`, 'Open', 'In Progress', 'Pending');
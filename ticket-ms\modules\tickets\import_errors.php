<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';

// Check if user has admin privileges
if (!isset($_SESSION['userdata']) || $_SESSION['userdata']['role'] !== 'admin') {
    http_response_code(403);
    echo '<div class="alert alert-danger">Access denied</div>';
    exit();
}

$importId = intval($_GET['import_id'] ?? 0);

if (!$importId) {
    echo '<div class="alert alert-danger">Invalid import ID</div>';
    exit();
}

try {
    // Get import details
    $importQuery = "SELECT * FROM import_history WHERE id = ?";
    $importStmt = $pdo->prepare($importQuery);
    $importStmt->execute([$importId]);
    $import = $importStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$import) {
        echo '<div class="alert alert-danger">Import record not found</div>';
        exit();
    }
    
    // Get errors for this import
    $errorsQuery = "SELECT * FROM import_errors WHERE import_history_id = ? ORDER BY row_number ASC";
    $errorsStmt = $pdo->prepare($errorsQuery);
    $errorsStmt->execute([$importId]);
    $errors = $errorsStmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    echo '<div class="alert alert-danger">Error loading import errors: ' . htmlspecialchars($e->getMessage()) . '</div>';
    exit();
}
?>

<div class="container-fluid">
    <div class="mb-3">
        <h6>Import Details</h6>
        <div class="row">
            <div class="col-md-6">
                <small class="text-muted">File:</small> <?php echo htmlspecialchars($import['original_filename']); ?><br>
                <small class="text-muted">Started:</small> <?php echo $import['started_at']; ?><br>
                <small class="text-muted">Status:</small> 
                <span class="badge badge-<?php echo $import['status'] === 'completed' ? 'success' : 'danger'; ?>">
                    <?php echo ucfirst($import['status']); ?>
                </span>
            </div>
            <div class="col-md-6">
                <small class="text-muted">Total Rows:</small> <?php echo number_format($import['total_rows']); ?><br>
                <small class="text-muted">Success:</small> <span class="text-success"><?php echo number_format($import['success_count']); ?></span><br>
                <small class="text-muted">Errors:</small> <span class="text-danger"><?php echo number_format($import['error_count']); ?></span>
            </div>
        </div>
    </div>
    
    <?php if (empty($errors)): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> No errors found for this import.
        </div>
    <?php else: ?>
        <div class="alert alert-warning">
            <i class="fas fa-exclamation-triangle"></i> 
            Found <?php echo count($errors); ?> error(s) during import.
        </div>
        
        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
            <table class="table table-sm table-striped">
                <thead class="thead-light sticky-top">
                    <tr>
                        <th style="width: 80px;">Row #</th>
                        <th>Error Message</th>
                        <th style="width: 150px;">Time</th>
                        <th style="width: 100px;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($errors as $error): ?>
                        <tr>
                            <td>
                                <span class="badge badge-danger"><?php echo $error['row_number']; ?></span>
                            </td>
                            <td>
                                <div class="text-wrap" style="max-width: 400px;">
                                    <?php echo htmlspecialchars($error['error_message']); ?>
                                </div>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <?php echo date('H:i:s', strtotime($error['created_at'])); ?>
                                </small>
                            </td>
                            <td>
                                <?php if (!empty($error['row_data'])): ?>
                                    <button class="btn btn-outline-info btn-sm" 
                                            onclick="showRowData(<?php echo htmlspecialchars($error['row_data']); ?>)"
                                            title="View Row Data">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Error Summary -->
        <div class="mt-3">
            <h6>Error Summary</h6>
            <?php
            // Group errors by type
            $errorTypes = [];
            foreach ($errors as $error) {
                $message = $error['error_message'];
                // Extract error type from message
                if (strpos($message, 'Customer number is required') !== false) {
                    $type = 'Missing Customer Number';
                } elseif (strpos($message, 'Invalid team assignment') !== false) {
                    $type = 'Invalid Team Assignment';
                } elseif (strpos($message, 'Invalid channel type') !== false) {
                    $type = 'Invalid Channel Type';
                } elseif (strpos($message, 'Issue details') !== false) {
                    $type = 'Missing Issue Details';
                } else {
                    $type = 'Other';
                }
                
                if (!isset($errorTypes[$type])) {
                    $errorTypes[$type] = 0;
                }
                $errorTypes[$type]++;
            }
            
            arsort($errorTypes);
            ?>
            
            <div class="row">
                <?php foreach ($errorTypes as $type => $count): ?>
                    <div class="col-md-4 mb-2">
                        <div class="card border-left-danger">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <small class="font-weight-bold"><?php echo htmlspecialchars($type); ?></small>
                                    </div>
                                    <div>
                                        <span class="badge badge-danger"><?php echo $count; ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- Common Solutions -->
        <div class="mt-3">
            <h6>Common Solutions</h6>
            <div class="alert alert-light">
                <ul class="mb-0 small">
                    <li><strong>Missing Customer Number:</strong> Ensure the customer number column is mapped correctly and contains valid data</li>
                    <li><strong>Invalid Team Assignment:</strong> Check that team IDs exist in the teams table, or leave the field empty for auto-assignment</li>
                    <li><strong>Invalid Channel Type:</strong> Use valid channel types (1=Monitoring, 2=Telephone, 3=Email, 4=Chat, 5=Walk-in)</li>
                    <li><strong>Missing Issue Details:</strong> Ensure either issue details or cause detail columns contain data</li>
                    <li><strong>Date Format Issues:</strong> Use standard date formats (YYYY-MM-DD or DD/MM/YYYY)</li>
                </ul>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Row Data Modal -->
<div class="modal fade" id="rowDataModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Row Data</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <pre id="rowDataContent" class="bg-light p-3" style="max-height: 400px; overflow-y: auto;"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function showRowData(rowData) {
    try {
        const formatted = JSON.stringify(rowData, null, 2);
        document.getElementById('rowDataContent').textContent = formatted;
        $('#rowDataModal').modal('show');
    } catch (e) {
        document.getElementById('rowDataContent').textContent = 'Error displaying row data: ' + e.message;
        $('#rowDataModal').modal('show');
    }
}
</script>

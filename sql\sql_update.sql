-- Add assigned_worker column to tickets table
ALTER TABLE tickets ADD COLUMN assigned_worker VARCHAR(50) DEFAULT NULL;

-- Copy existing team assignments to individual workers if needed
-- This is just a placeholder - you may need a more complex migration strategy
-- UPDATE tickets SET assigned_worker = 'default_username' WHERE assigned_team IS NOT NULL;

-- Create a record of the change in ticket_forwards table
ALTER TABLE ticket_forwards ADD COLUMN from_worker VARCHAR(50) DEFAULT NULL;
ALTER TABLE ticket_forwards ADD COLUMN to_worker VARCHAR(50) DEFAULT NULL;
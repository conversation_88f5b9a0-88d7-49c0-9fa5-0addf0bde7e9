-- --------------------------------------------------------
-- Host:                         ************
-- Server version:               10.5.27-MariaDB - MariaDB Server
-- Server OS:                    Linux
-- HeidiSQL Version:             12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREI<PERSON><PERSON>_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- Dumping structure for table ticket_management.users_division
CREATE TABLE IF NOT EXISTS `users_division` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` tinytext NOT NULL,
  `create_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `name` (`name`(255))
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Dumping data for table ticket_management.users_division: ~10 rows (approximately)
INSERT IGNORE INTO `users_division` (`id`, `name`, `create_at`) VALUES
	(1, 'Business Solution', '2025-08-06 03:34:10'),
	(2, 'Center', '2025-08-06 03:34:10'),
	(3, 'CEO', '2025-08-06 03:34:10'),
	(4, 'CEO\'s Office', '2025-08-06 03:34:10'),
	(5, 'Financial', '2025-08-06 03:34:10'),
	(6, 'Operation', '2025-08-06 03:34:10'),
	(7, 'Product', '2025-08-06 03:34:10'),
	(8, 'Products', '2025-08-06 03:34:10'),
	(9, 'Sales', '2025-08-06 03:34:10'),
	(10, 'System Integration', '2025-08-06 03:34:10');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;

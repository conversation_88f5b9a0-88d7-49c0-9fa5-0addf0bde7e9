-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: ************    Database: ticket_management
-- ------------------------------------------------------
-- Server version	5.5.5-10.5.27-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `ticket_sub_cause`
--

DROP TABLE IF EXISTS `ticket_sub_cause`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ticket_sub_cause` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` tinytext NOT NULL,
  `update_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `title` (`title`(255))
) ENGINE=InnoDB AUTO_INCREMENT=70 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ticket_sub_cause`
--

LOCK TABLES `ticket_sub_cause` WRITE;
/*!40000 ALTER TABLE `ticket_sub_cause` DISABLE KEYS */;
INSERT INTO `ticket_sub_cause` VALUES (1,'Advance notice maintanance plan','2025-07-14 05:30:18'),(2,'Application','2025-07-14 05:30:18'),(3,'Authentication server problem','2025-07-14 05:30:18'),(4,'Block Service Port','2025-07-14 05:30:18'),(5,'Copper cable problem','2025-07-14 05:30:18'),(6,'Core network problem','2025-07-14 05:30:18'),(7,'Customer ask for help in using the service','2025-07-14 05:30:18'),(8,'DNS server problem','2025-07-14 05:30:18'),(9,'Electrical problems','2025-07-14 05:30:18'),(10,'Electrical switch off','2025-07-14 05:30:18'),(11,'E-mail server problem','2025-07-14 05:30:18'),(12,'Equipment problem','2025-07-14 05:30:18'),(13,'Exceed concurrent connection','2025-07-14 05:30:18'),(14,'Full bandwidth','2025-07-14 05:30:18'),(15,'Hosting Provider problem','2025-07-14 05:30:18'),(16,'Incorrect connection of devices in the network','2025-07-14 05:30:18'),(17,'Installation period','2025-07-14 05:30:18'),(18,'Internet link problem','2025-07-14 05:30:18'),(19,'IP Address blacklist','2025-07-14 05:30:18'),(20,'Maintanance network','2025-07-14 05:30:18'),(21,'Monitor System','2025-07-14 05:30:18'),(22,'NAT44','2025-07-14 05:30:18'),(23,'Network configuration','2025-07-14 05:30:18'),(24,'Network problem','2025-07-14 05:30:18'),(25,'Optic fiber cable problem','2025-07-14 05:30:18'),(26,'Overdue payment for Ji-net service','2025-07-14 05:30:18'),(27,'Overdue payment for media provider service','2025-07-14 05:30:18'),(28,'Radius server problem','2025-07-14 05:30:18'),(29,'Sim Problem','2025-07-14 05:30:18'),(30,'Sim Terminate','2025-07-14 05:30:18'),(31,'Site Access Point Fail','2025-07-14 05:30:18'),(32,'Site Access Point Hang','2025-07-14 05:30:18'),(33,'Site aircard fail','2025-07-14 05:30:18'),(34,'Site Aircard Hang','2025-07-14 05:30:18'),(35,'Site copper cable problem','2025-07-14 05:30:18'),(36,'Site equipment configuration problem','2025-07-14 05:30:18'),(37,'Site equipment fail','2025-07-14 05:30:18'),(38,'Site Equipment Hang','2025-07-14 05:30:18'),(39,'Site fiber optic cable problem','2025-07-14 05:30:18'),(40,'Site firewall fail','2025-07-14 05:30:18'),(41,'Site Firewall Hang','2025-07-14 05:30:18'),(42,'Site Router Fail','2025-07-14 05:30:18'),(43,'Site Router Hang','2025-07-14 05:30:18'),(44,'Site switch/hub fail','2025-07-14 05:30:18'),(45,'Site switch/hub hang','2025-07-14 05:30:18'),(46,'Site ups fail','2025-07-14 05:30:18'),(47,'Site ups hang','2025-07-14 05:30:18'),(48,'Temporary closed','2025-07-14 05:30:18'),(49,'Terminate','2025-07-14 05:30:18'),(50,'Turn off the device','2025-07-14 05:30:18'),(51,'Unidentified','2025-07-14 05:30:18'),(52,'User profile problem','2025-07-14 05:30:18'),(53,'Ticket System','2025-07-14 05:30:18'),(54,'Site Adapter fail','2025-07-14 05:30:18'),(55,'Lan Problem','2025-07-14 05:30:18'),(56,'Power Outage at Customer Site','2025-07-14 05:30:18'),(57,'Plugs Disconnect','2025-07-14 05:30:18'),(58,'Natural Disaster','2025-07-14 05:30:18'),(59,'Renovate','2025-07-14 05:30:18'),(60,'IN House Wiring','2025-07-14 05:30:18'),(61,'Change Solution','2025-07-14 05:30:18'),(62,'Customer has an outstanding balance','2025-07-14 05:30:18'),(63,'Service Problem','2025-07-14 05:30:18'),(64,'Equipment Hang','2025-07-14 05:30:18'),(65,'Service Port Error','2025-07-14 05:30:18'),(66,'Internet Customer Site','2025-07-14 05:30:18'),(67,'Summarize the information for Cybertron.','2025-07-14 05:30:18'),(68,'The site router is misconfigured by a Meraki Cloud issue.','2025-07-14 05:30:18'),(69,'Faulty UTP uplink cable to ISP gateway.','2025-07-14 05:30:18');
/*!40000 ALTER TABLE `ticket_sub_cause` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-18 16:44:55

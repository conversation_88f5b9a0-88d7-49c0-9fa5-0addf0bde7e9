<?php
session_start();
/*
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
*/

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Shared\Date;

// Check if user has admin privileges
if (!isset($_SESSION['userdata']) || $_SESSION['userdata']['role'] !== 'admin') {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Access denied']);
    exit();
}

header('Content-Type: application/json');

try {
    $action = $_POST['action'] ?? '';

    if ($action === 'upload') {
        handleFileUpload();
    } elseif ($action === 'import') {
        handleDataImport();
    } else {
        throw new Exception('Invalid action');
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

function handleFileUpload()
{
    if (!isset($_FILES['excel_file']) || $_FILES['excel_file']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File upload failed');
    }

    $file = $_FILES['excel_file'];
    $allowedTypes = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];

    if (!in_array($file['type'], $allowedTypes) && !preg_match('/\.(xlsx|xls)$/i', $file['name'])) {
        throw new Exception('Invalid file type. Please upload an Excel file (.xlsx or .xls)');
    }

    if ($file['size'] > 10 * 1024 * 1024) { // 10MB limit
        throw new Exception('File size too large. Maximum size is 10MB');
    }

    // Create uploads directory if it doesn't exist
    $uploadDir = '../../uploads/imports/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // Generate unique filename
    $filename = uniqid('import_') . '_' . $file['name'];
    $filepath = $uploadDir . $filename;

    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        throw new Exception('Failed to save uploaded file');
    }

    // Read Excel file
    try {
        $spreadsheet = IOFactory::load($filepath);
        $worksheet = $spreadsheet->getActiveSheet();
        $data = $worksheet->toArray();

        if (empty($data)) {
            throw new Exception('Excel file is empty');
        }

        // Get column headers (first row)
        $columns = array_filter($data[0], function ($value) {
            return !empty(trim($value));
        });

        if (empty($columns)) {
            throw new Exception('No column headers found in the first row');
        }

        // Store file info in session for later use
        $_SESSION['import_file'] = $filepath;
        $_SESSION['import_data'] = $data;

        echo json_encode([
            'success' => true,
            'columns' => array_values($columns),
            'total_rows' => count($data) - 1, // Exclude header row
            'filename' => $filename
        ]);
    } catch (Exception $e) {
        // Clean up uploaded file on error
        if (file_exists($filepath)) {
            unlink($filepath);
        }
        throw new Exception('Error reading Excel file: ' . $e->getMessage());
    }
}

function handleDataImport()
{
    global $pdo;

    if (!isset($_SESSION['import_data']) || !isset($_SESSION['import_file'])) {
        throw new Exception('No file data found. Please upload a file first.');
    }

    $data = $_SESSION['import_data'];
    $mapping = $_POST['mapping'] ?? [];
    $skipFirstRow = ($_POST['skip_first_row'] ?? '0') === '1';
    $validateData = ($_POST['validate_data'] ?? '0') === '1';
    $createMissingCustomers = ($_POST['create_missing_customers'] ?? '0') === '1';
    $filename = $_POST['filename'] ?? 'unknown';
   /*  
    echo json_encode([
        'success' => true,
        'mapping' => $mapping,
        'data rows' => $data
    ]);
    exit();
 */

    if (empty($mapping)) {
        throw new Exception('No column mappings provided');
    }


    // Remove empty mappings
    $mapping = array_filter($mapping, function ($value) {
        return $value !== '';
    });


    if (empty($mapping)) {
        throw new Exception('No valid column mappings provided');
    }

    $startRow = $skipFirstRow ? 1 : 0;
    $totalRows = count($data) - $startRow;
    $successCount = 0;
    $errorCount = 0;
    $errors = [];

    // Create import history record
    $importHistoryId = createImportHistory($pdo, $filename, $totalRows);

    // Get reference data
    $teams = getTeams($pdo);
    $channelTypes = getChannelTypes($pdo);
    $customers = getCustomers($pdo);

    try {
        $pdo->beginTransaction();

        // Update import status to processing
        updateImportStatus($pdo, $importHistoryId, 'processing');

        for ($i = $startRow; $i < count($data); $i++) {
            $row = $data[$i];

            try {
                // Skip empty rows
                if (empty(array_filter($row))) {
                    continue;
                }

                // Map row data to database fields
                $ticketData = mapRowData($row, $mapping);
                //SELECT Ref, CusCode FROM KCS_DB.Main WHERE Ref='BIPVB4210401'; 
                //customer_number
                $ticketData['customer_number'] = ' ';
                $csno = $ticketData['csno'] ?? '';
                if (!empty($csno)) {
                    $customer = getCustomerByRef($pdo, $csno);
                    if ($customer) {
                        $ticketData['customer_number'] = $customer['CusCode'];
                    }
                }
                
                $ticketData['affecting_service'] = normalizeAffectingService($ticketData['effect']);

                if ($validateData) {
                    validateTicketData($ticketData, $teams, $channelTypes, $customers);
                }
/* 
                //debug
                   echo json_encode([
                    'success' => true,
                    'total_rows' => $totalRows,
                    'success_count' => $successCount,
                    'error_count' => $errorCount,
                    'errors' => $errors,
                    'import_id' => $importHistoryId,
                    'row' => $row,
                    'ticketData' => $ticketData
                ]);
                exit();  */ 

                // Insert ticket
                insertTicket($pdo, $ticketData);
                $successCount++;
            } catch (Exception $e) {
                $errorCount++;
                $errorMessage = "Row " . ($i + 1) . ": " . $e->getMessage();
                $errors[] = $errorMessage;

                // Log detailed error to database
                logImportError($pdo, $importHistoryId, $i + 1, $e->getMessage(), $row);

                // Stop if too many errors
                if ($errorCount > 50) {
                    $errors[] = "Too many errors. Import stopped.";
                    break;
                }
            }
        }

        // Update import history with final results
        $status = $errorCount > 0 ? 'completed' : 'completed';
        updateImportHistory($pdo, $importHistoryId, $status, $successCount, $errorCount);

        $pdo->commit();

        // Clean up
        if (file_exists($_SESSION['import_file'])) {
            unlink($_SESSION['import_file']);
        }
        unset($_SESSION['import_file']);
        unset($_SESSION['import_data']);

        echo json_encode([
            'success' => true,
            'total_rows' => $totalRows,
            'success_count' => $successCount,
            'error_count' => $errorCount,
            'errors' => $errors,
            'import_id' => $importHistoryId
        ]);
    } catch (Exception $e) {
        $pdo->rollBack();

        // Update import status to failed
        try {
            updateImportStatus($pdo, $importHistoryId, 'failed', $e->getMessage());
        } catch (Exception $updateError) {
            error_log("Failed to update import status: " . $updateError->getMessage());
        }

        throw $e;
    }
}

function mapRowData($row, $mapping)
{
    $ticketData = [];

    foreach ($mapping as $field => $columnIndex) {
        if (isset($row[$columnIndex])) {
            $value = trim($row[$columnIndex]);

            // Handle special field conversions
            switch ($field) {
                case 'ticket_open':
                case 'ticket_close':
                    $ticketData[$field] = convertExcelDate($value);
                    break;
                case 'priority':
                    $ticketData[$field] = normalizePriority($value);
                    break;
                case 'status':
                    $ticketData[$field] = normalizeStatus($value);
                    break;
                case 'channel_type':
                    $ticketData[$field] = normalizeChannelType($value);
                    break;
                case 'affecting_service':
                    $ticketData[$field] = normalizeAffectingService($value);
                    break;
                case 'customer_number':
                    $ticketData[$field] = '0';
                    break;
                default:
                    $ticketData[$field] = $value;
            }
        }
    }

    return $ticketData;
}

function convertExcelDate($value)
{
    if (empty($value)) return null;

    // If it's already a date string, try to parse it
    if (!is_numeric($value)) {
        $date = DateTime::createFromFormat('Y-m-d H:i:s', $value);
        if (!$date) {
            $date = DateTime::createFromFormat('Y-m-d', $value);
        }
        if (!$date) {
            $date = DateTime::createFromFormat('d/m/Y', $value);
        }
        if (!$date) {
            $date = DateTime::createFromFormat('m/d/Y', $value);
        }
        return $date ? $date->format('Y-m-d H:i:s') : null;
    }

    // Handle Excel date serial number
    try {
        $date = Date::excelToDateTimeObject($value);
        return $date->format('Y-m-d H:i:s');
    } catch (Exception $e) {
        return null;
    }
}

function normalizePriority($value)
{
    $value = strtolower(trim($value));
    if (in_array($value, ['high', 'สูง', 'urgent'])) return 'High';
    if (in_array($value, ['medium', 'กลาง', 'normal'])) return 'Medium';
    if (in_array($value, ['low', 'ต่ำ', 'minor'])) return 'Low';
    return 'Medium'; // Default
}

function normalizeStatus($value)
{
    $value = strtolower(trim($value));
    if (in_array($value, ['open', 'เปิด', 'new'])) return 'Open';
    if (in_array($value, ['in progress', 'กำลังดำเนินการ', 'working'])) return 'In Progress';
    if (in_array($value, ['pending', 'รอ', 'waiting'])) return 'Pending';
    if (in_array($value, ['closed', 'ปิด', 'resolved', 'completed'])) return 'Closed';
    return 'Open'; // Default
}

function normalizeChannelType($value)
{
    $value = strtolower(trim($value));
    if (in_array($value, ['monitoring','monitor system'])) return 1;
    if (in_array($value, ['telephone','call center', 'phone'])) return 2;
    if (in_array($value, ['email','e-mail' ,'อีเมล'])) return 3;
    if (in_array($value, ['chat', 'แชท'])) return 4;
    if (in_array($value, ['walk-in', 'other', 'เดินเข้ามา'])) return 5;
    return 2; // Default to telephone
}
function normalizeAffectingService($value)
{
    $value = strtolower(trim($value));
    if (in_array($value, ['กระทบ', 'กระทบบริการ','impacted'])) return 1;
    return 2; // Default to not impact
}

function getTeams($pdo)
{
    $stmt = $pdo->query("SELECT id, name FROM teams WHERE 1");
    return $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
}

function getChannelTypes($pdo)
{
    $stmt = $pdo->query("SELECT id, name FROM channel_types WHERE is_active = 1");
    return $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
}

function getCustomers($pdo)
{
    $stmt = $pdo->query("SELECT CusCode, CusName FROM KCS_DB.Customers WHERE 1");
    return $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
}

function validateTicketData($data, $teams, $channelTypes, $customers)
{
    // Validate required fields
    if (empty($data['customer_number'])) {
        throw new Exception('Customer number is required');
    }

    if (empty($data['issue_details']) && empty($data['cause_detail'])) {
        throw new Exception('Issue details or cause detail is required');
    }

    // Validate team assignment
    if (!empty($data['assigned_team']) && !isset($teams[$data['assigned_team']])) {
        throw new Exception('Invalid team assignment: ' . $data['assigned_team']);
    }

    // Validate channel type
    if (!empty($data['channel_type']) && !isset($channelTypes[$data['channel_type']])) {
        throw new Exception('Invalid channel type: ' . $data['channel_type']);
    }
}

function createCustomerIfNotExists($pdo, $customerNumber)
{
    // Check if customer exists in KCS_DB
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM KCS_DB.Customers WHERE CusCode = ?");
    $stmt->execute([$customerNumber]);

    if ($stmt->fetchColumn() == 0) {
        // Create basic customer record
        $insertStmt = $pdo->prepare("INSERT INTO KCS_DB.Customers (CusCode, CusName, status) VALUES (?, ?, 'active')");
        $insertStmt->execute([$customerNumber, 'Imported Customer - ' . $customerNumber]);
    }
}

function isValidDateTime($text, $format = 'Y-m-d H:i:s')
{
    $dt = DateTime::createFromFormat($format, $text);
    return $dt && $dt->format($format) === $text;
}

function insertTicket($pdo, $data)
{
    // Generate ticket number
    $ticketNumber = $data['ticket_number']; //generateTicketNumber($pdo);

    $status_name = 'Open';
    if (isValidDateTime($data['ticket_close'])) {
        $status_name = 'Closed';
    }
    // Prepare basic ticket data
    $ticketData = [
        'ticket_number' => $ticketNumber,
        'customer_number' => $data['customer_number'] ?? '',
        'issue_details' => $data['issue_details'] ?? $data['cause_detail'] ?? 'Not found issue detail',
        'priority' => $data['priority'] ?? 'Medium',
        'status' =>  $status_name,
        'assigned_team' => $data['assigned_team'] ?? 2,
        'username' => $data['noc_receiver'],
        'symptoms_details' => $data['symptoms_details'] ?? null,
        'affecting_service' => $data['affecting_service'] ?? 2, // Default to not impact
        'product_type' => $data['product_type'] ?? null,
        'channel_type' => $data['channel_type'] ?? 2, // Default to telephone
        'csno' => $data['csno'] ?? '',
        'created_at' => $data['ticket_open'] ?? date('Y-m-d H:i:s'),
        'is_import' => 1,
        'closed_at' => $data['ticket_close'] ?? null,
        'updated_by' => $data['noc_receiver'] ?? null
    ];

    // Insert main ticket
    $query = "INSERT INTO tickets (
        ticket_number, 
        customer_number, 
        issue_details, 
        priority, 
        status, 
        assigned_team, 
        username,
        symptoms_details,
        affecting_service,
        product_type, 
        channel_type, 
        csno, 
        created_at,
        is_import,
        closed_at,
        updated_by
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,?,?,?,?)";
/*
 echo json_encode([
            'success' => true,
            'query' => $query,
            'ticketData' => $ticketData
        ]);
exit();
*/
    $stmt = $pdo->prepare($query);
    $stmt->execute(array_values($ticketData));

    // Insert extended data if available
    insertExtendedTicketData($pdo, $ticketNumber, $data);

    // Insert status history if ticket is closed
    if (!empty($data['ticket_close'])) {
        insertStatusHistory($pdo, $ticketNumber, $data['ticket_close']);
    }
}

function insertExtendedTicketData($pdo, $ticketNumber, $data)
{
    // Check if we have extended data to insert
    $extendedFields = [
        'contact_person',
        'location',
        'effect',
        'project_name',
        'owner',
        'noc_receiver',
        'noc_closer',
        'main_cause',
        'sub_cause',
        'group_cause1',
        'group_cause2',
        'cause_detail',
        'cause_detail_eng',
        'provider',
        'province',
        'downtime_hr',
        'downtime_min',
        'total_time',
        'stop_clock',
        'category'
    ];
    /*
    $hasExtendedData = false;
    foreach ($extendedFields as $field) {
        if (!empty($data[$field])) {
            $hasExtendedData = true;
            break;
        }
    }

    if (!$hasExtendedData) return;
*/

    // Insert into a new extended table (we'll create this)
    $query = "INSERT INTO ticket_extended (
        ticket_number, contact_person, location, effect, project_name, owner,
        noc_receiver, noc_closer, main_cause, sub_cause, group_cause1, group_cause2,
        cause_detail, cause_detail_eng, provider, province, downtime_hr, downtime_min,
        total_time, stop_clock, category, created_at, created_by
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

    try {
        $stmt = $pdo->prepare($query);

        $stmt->execute([
            $ticketNumber,
            $data['contact_person'] ?? null,
            $data['location'] ?? null,
            $data['effect'] ?? null,
            $data['project_name'] ?? null,
            $data['owner'] ?? null,
            $data['noc_receiver'] ?? null,
            $data['noc_closer'] ?? null,
            $data['main_cause'] ?? null,
            $data['sub_cause'] ?? null,
            $data['group_cause1'] ?? null,
            $data['group_cause2'] ?? null,
            $data['cause_detail'] ?? null,
            $data['cause_detail_eng'] ?? null,
            $data['provider'] ?? null,
            $data['province'] ?? null,
            $data['downtime_hr'] ?? null,
            $data['downtime_min'] ?? null,
            $data['total_time'] ?? null,
            $data['stop_clock'] ?? null,
            $data['category'] ?? null,
            $data['ticket_open'] ?? date('Y-m-d H:i:s'),
            $data['noc_receiver'] ?? null
        ]);
    } catch (Exception $e) {
        /*
        echo json_encode([
            'func' =>'insert ticket_extended',
            'errors' => $e->getMessage()
        ]);
        exit;
*/
        // If table doesn't exist, we'll handle this in the database schema extension task
        error_log("Extended data insert failed: " . $e->getMessage());
        //return $e->getMessage();
    }

    // Also insert SIM data if available
    if (!empty($data['login_temp']) || !empty($data['sim_no']) || !empty($data['sim_serial']) || !empty($data['sim_operator'])) {
        $simQuery = "INSERT INTO ticket_interim (
            ticket_number, login_temp, sim_no, sim_serial, sim_operator, created_at, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)";

        try {
            $simStmt = $pdo->prepare($simQuery);
            $simStmt->execute([
                $ticketNumber,
                $data['login_temp'] ?? null,
                $data['sim_no'] ?? null,
                $data['sim_serial'] ?? null,
                $data['sim_operator'] ?? null,
                $data['ticket_open'] ?? date('Y-m-d H:i:s'),
                $data['noc_receiver'] ?? null
            ]);
        } catch (Exception $e) {
            /*
            echo json_encode([
                'func' =>'insert ticket_interim',
                'errors' => $e->getMessage()
            ]);
            exit;
*/
            error_log("SIM data insert failed: " . $e->getMessage());
           // return $e->getMessage();
        }
    } else {
        // 
    }
}

function insertStatusHistory($pdo, $ticketNumber, $closeDate)
{
    $query = "INSERT INTO ticket_status_history (ticket_number, status, changed_by, changed_at, comments)
              VALUES (?, 'Closed', ?, ?, 'Imported as closed ticket')";

    $stmt = $pdo->prepare($query);
    $stmt->execute([$ticketNumber, $_SESSION['user_id'], $closeDate]);
}

function createImportHistory($pdo, $filename, $totalRows)
{
    $query = "INSERT INTO import_history (
        filename, original_filename, total_rows, import_type, status, imported_by, started_at
    ) VALUES (?, ?, ?, 'tickets', 'pending', ?, NOW())";

    $stmt = $pdo->prepare($query);
    $stmt->execute([
        $filename,
        $filename,
        $totalRows,
        $_SESSION['user_id']
    ]);

    return $pdo->lastInsertId();
}

function updateImportStatus($pdo, $importId, $status, $errorDetails = null)
{
    $query = "UPDATE import_history SET status = ?, error_details = ? WHERE id = ?";
    $stmt = $pdo->prepare($query);
    $stmt->execute([$status, $errorDetails, $importId]);
}

function updateImportHistory($pdo, $importId, $status, $successCount, $errorCount)
{
    $query = "UPDATE import_history SET
        status = ?,
        success_count = ?,
        error_count = ?,
        completed_at = NOW()
        WHERE id = ?";

    $stmt = $pdo->prepare($query);
    $stmt->execute([$status, $successCount, $errorCount, $importId]);
}

function logImportError($pdo, $importId, $rowNumber, $errorMessage, $rowData)
{
    try {
        $query = "INSERT INTO import_errors (
            import_history_id, row_number, error_message, row_data, created_at
        ) VALUES (?, ?, ?, ?, NOW())";

        $stmt = $pdo->prepare($query);
        $stmt->execute([
            $importId,
            $rowNumber,
            $errorMessage,
            json_encode($rowData)
        ]);
    } catch (Exception $e) {
        // If import_errors table doesn't exist, just log to error log
        error_log("Import error logging failed: " . $e->getMessage());
    }
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loading Status Examples</title>
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        /* Example 1: Simple Spinner */
        .loading-overlay-simple {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .simple-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Example 2: Advanced Loading with Progress */
        .loading-overlay-advanced {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 123, 255, 0.1), rgba(0, 86, 179, 0.1));
            backdrop-filter: blur(5px);
            z-index: 9999;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }

        .advanced-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid transparent;
            border-top: 4px solid #007bff;
            border-right: 4px solid #28a745;
            border-radius: 50%;
            animation: spin 1.5s linear infinite;
            margin-bottom: 20px;
        }

        .loading-card {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 300px;
        }

        /* Example 3: Dots Loading */
        .dots-loading {
            display: inline-block;
        }

        .dots-loading span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #007bff;
            margin: 0 2px;
            animation: dots 1.4s infinite ease-in-out both;
        }

        .dots-loading span:nth-child(1) { animation-delay: -0.32s; }
        .dots-loading span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes dots {
            0%, 80%, 100% {
                transform: scale(0);
                opacity: 0.5;
            }
            40% {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Progress Bar */
        .progress-container {
            width: 250px;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            margin: 15px 0;
            overflow: hidden;
        }

        .progress-bar-animated {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            width: 0%;
            border-radius: 3px;
            animation: progress 3s ease-in-out;
        }

        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        /* Fade animations */
        .fade-out {
            opacity: 0;
            transition: opacity 0.5s ease-out;
        }

        .page-content {
            opacity: 0;
            transition: opacity 0.5s ease-in;
        }

        .page-content.loaded {
            opacity: 1;
        }

        /* Demo buttons */
        .demo-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>Loading Status Examples</h1>
        <p class="lead">Different loading implementations you can use in your ticket system.</p>

        <!-- Demo Buttons -->
        <div class="demo-section">
            <h3>Demo Loading Types</h3>
            <button class="btn btn-primary mr-2" onclick="showSimpleLoading()">Simple Loading</button>
            <button class="btn btn-success mr-2" onclick="showAdvancedLoading()">Advanced Loading</button>
            <button class="btn btn-info mr-2" onclick="showProgressLoading()">Progress Loading</button>
            <button class="btn btn-warning" onclick="hideAllLoading()">Hide Loading</button>
        </div>

        <!-- Code Examples -->
        <div class="demo-section">
            <h3>1. Simple Spinner (Minimal)</h3>
            <pre><code>&lt;!-- Simple Loading Overlay --&gt;
&lt;div id="simple-loading" style="display: none;"&gt;
    &lt;div class="loading-overlay-simple"&gt;
        &lt;div class="simple-spinner"&gt;&lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;

&lt;script&gt;
// Show loading
document.getElementById('simple-loading').style.display = 'block';

// Hide loading when page loads
window.addEventListener('load', function() {
    document.getElementById('simple-loading').style.display = 'none';
});
&lt;/script&gt;</code></pre>
        </div>

        <div class="demo-section">
            <h3>2. Advanced Loading with Progress</h3>
            <pre><code>&lt;!-- Advanced Loading Overlay --&gt;
&lt;div id="advanced-loading"&gt;
    &lt;div class="loading-overlay-advanced"&gt;
        &lt;div class="loading-card"&gt;
            &lt;div class="advanced-spinner"&gt;&lt;/div&gt;
            &lt;h5&gt;Loading Ticket Data...&lt;/h5&gt;
            &lt;div class="progress-container"&gt;
                &lt;div class="progress-bar-animated"&gt;&lt;/div&gt;
            &lt;/div&gt;
            &lt;p class="text-muted"&gt;Please wait while we fetch your data&lt;/p&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
        </div>

        <div class="demo-section">
            <h3>3. Dots Loading Animation</h3>
            <div class="text-center p-3">
                <span>Loading</span>
                <div class="dots-loading">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>4. JavaScript Implementation for Your Ticket Page</h3>
            <pre><code>// Add this to your ticket.php page
document.addEventListener('DOMContentLoaded', function() {
    // Show loading overlay
    const overlay = document.getElementById('loading-overlay');
    
    // Update loading text with steps
    const steps = [
        'Loading Ticket Information...',
        'Fetching Customer Data...',
        'Loading Comments...',
        'Preparing Interface...'
    ];
    
    let currentStep = 0;
    const stepInterval = setInterval(() => {
        if (currentStep < steps.length - 1) {
            currentStep++;
            document.querySelector('.loading-text').textContent = steps[currentStep];
        }
    }, 600);
    
    // Hide loading when everything is ready
    window.addEventListener('load', function() {
        setTimeout(() => {
            overlay.classList.add('fade-out');
            document.querySelector('.page-content').classList.add('loaded');
            
            setTimeout(() => {
                overlay.style.display = 'none';
            }, 500);
            
            clearInterval(stepInterval);
        }, 500);
    });
});</code></pre>
        </div>
    </div>

    <!-- Loading Overlays for Demo -->
    <div id="simple-loading" style="display: none;">
        <div class="loading-overlay-simple">
            <div class="simple-spinner"></div>
        </div>
    </div>

    <div id="advanced-loading" style="display: none;">
        <div class="loading-overlay-advanced">
            <div class="loading-card">
                <div class="advanced-spinner"></div>
                <h5>Loading Ticket Data...</h5>
                <div class="progress-container">
                    <div class="progress-bar-animated"></div>
                </div>
                <p class="text-muted">Please wait while we fetch your data</p>
            </div>
        </div>
    </div>

    <div id="progress-loading" style="display: none;">
        <div class="loading-overlay-advanced">
            <div class="loading-card">
                <i class="fas fa-ticket-alt fa-3x text-primary mb-3"></i>
                <h5 id="progress-text">Initializing...</h5>
                <div class="progress-container">
                    <div id="progress-bar" style="width: 0%; height: 100%; background: #007bff; border-radius: 3px; transition: width 0.3s;"></div>
                </div>
                <p class="text-muted">Processing your request</p>
            </div>
        </div>
    </div>

    <script>
        function showSimpleLoading() {
            hideAllLoading();
            document.getElementById('simple-loading').style.display = 'block';
            setTimeout(() => hideAllLoading(), 3000);
        }

        function showAdvancedLoading() {
            hideAllLoading();
            document.getElementById('advanced-loading').style.display = 'block';
            setTimeout(() => hideAllLoading(), 4000);
        }

        function showProgressLoading() {
            hideAllLoading();
            const loading = document.getElementById('progress-loading');
            const progressText = document.getElementById('progress-text');
            const progressBar = document.getElementById('progress-bar');
            
            loading.style.display = 'block';
            
            const steps = [
                { text: 'Connecting to database...', progress: 20 },
                { text: 'Loading ticket data...', progress: 40 },
                { text: 'Fetching comments...', progress: 60 },
                { text: 'Preparing interface...', progress: 80 },
                { text: 'Almost ready...', progress: 100 }
            ];
            
            let currentStep = 0;
            const stepInterval = setInterval(() => {
                if (currentStep < steps.length) {
                    progressText.textContent = steps[currentStep].text;
                    progressBar.style.width = steps[currentStep].progress + '%';
                    currentStep++;
                } else {
                    clearInterval(stepInterval);
                    setTimeout(() => hideAllLoading(), 1000);
                }
            }, 800);
        }

        function hideAllLoading() {
            document.getElementById('simple-loading').style.display = 'none';
            document.getElementById('advanced-loading').style.display = 'none';
            document.getElementById('progress-loading').style.display = 'none';
        }
    </script>
</body>
</html>

<?php
header('Content-Type: application/json');

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

$response = ['success' => false, 'message' => ''];
// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get POST data
$template_id = isset($_POST['template_id']) ? (int)$_POST['template_id'] : 0;
$status = isset($_POST['status']) ? (int)$_POST['status'] : 0;

// Validate input
if ($template_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid template ID']);
    exit;
}

// Validate status (should be 0 or 1)
if ($status !== 0 && $status !== 1) {
    echo json_encode(['success' => false, 'message' => 'Invalid status value']);
    exit;
}

try {
    // Update the status in the database
    $query = "UPDATE ticket_cause_template SET status = :status WHERE id = :template_id";
    $stmt = $pdo->prepare($query);
    $stmt->bindParam(':status', $status, PDO::PARAM_INT);
    $stmt->bindParam(':template_id', $template_id, PDO::PARAM_INT);
    
    if ($stmt->execute()) {
        // Check if any row was affected
        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true, 
                'message' => 'Status updated successfully',
                'template_id' => $template_id,
                'status' => $status
            ]);
        } else {
            echo json_encode(['success' => false, 'message' => 'Template not found or no changes made']);
        }
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update status']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>

<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if ticket ID is provided
$ticket_id = $_GET['tkt_id'] ?? null;
if (!$ticket_id) {
    header('Location: list.php?error=No ticket specified');
    exit;
}

// Fetch ticket details
$query = "SELECT t.*, 
          c.CusName AS customer_name,
          c.CusAddress AS customer_address,
          tm.name AS team_name,
          u.fullname AS assigned_worker_name,
          m.Phone1 AS Customer_contact,
          m.Site_Name
          FROM tickets t
          LEFT JOIN KCS_DB.Main m ON t.csno = m.Ref
          LEFT JOIN KCS_DB.Customers c ON t.customer_number = c.CusCode
          LEFT JOIN teams tm ON t.assigned_team = tm.id
          LEFT JOIN users u ON t.assigned_worker = u.username
          WHERE t.ticket_number = ?";
$stmt = $pdo->prepare($query);
$stmt->execute([$ticket_id]);
$ticket = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$ticket) {
    header('Location: list.php?error=Ticket not found');
    exit;
}

// Get teams for dropdown
$teams_query = "SELECT id, name FROM teams ORDER BY name";
$teams_result = $pdo->query($teams_query);
$teams = $teams_result->fetchAll(PDO::FETCH_ASSOC);

// After the teams query, add these queries
$symptoms_query = "SELECT id, code, name FROM symptoms WHERE is_active = 1 ORDER BY name";
$symptoms_result = $pdo->query($symptoms_query);
$symptoms = $symptoms_result->fetchAll(PDO::FETCH_ASSOC);

$product_types_query = "SELECT id, code, name FROM product_types WHERE is_active = 1 ORDER BY name";
$product_types_result = $pdo->query($product_types_query);
$product_types = $product_types_result->fetchAll(PDO::FETCH_ASSOC);

// Get channel types for dropdown
$channel_types_query = "SELECT id, name FROM channel_types WHERE is_active = 1 ORDER BY name";
$channel_types_result = $pdo->query($channel_types_query);
$channel_types = $channel_types_result->fetchAll(PDO::FETCH_ASSOC);

// get ticket type
$ticket_type_query = "SELECT id, name FROM ticket_type WHERE is_active = 1 ORDER BY name";
$ticket_type_result = $pdo->query($ticket_type_query);
$ticket_type = $ticket_type_result->fetchAll(PDO::FETCH_ASSOC);


// After fetching ticket details, add this query to get workers
$workers_query = "SELECT * FROM users 
                 WHERE status='active' AND division='Operation'
                 ORDER BY fullname";
$workers_result = $pdo->query($workers_query);
$workers = $workers_result->fetchAll(PDO::FETCH_ASSOC);

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    //var_dump($_POST);
    // Initialize error array
    $errors = [];
    $customer_number = $_POST["customer_number"];
    $contact_name = trim($_POST["ticket_contact_name"] ?? '');
    $issue_details = trim($_POST["issue_details"]);
    $priority = $_POST["priority"];
    $assigned_worker = $_POST["assigned_worker"]; // Changed from assigned_team
    $open_at = trim($_POST['open_at'] ?? date('Y-m-d H:i:s')); // Default to current time if not provided

    $status = $_POST["status"];
    $ticket_type = $_POST["ticket_type"];
    $affecting_service = $_POST["affecting_service"];
    $symptoms_details = $_POST["Symptomsdetails"];
    $product_type = $_POST["product_type"];
    $severity = $_POST["severity"];
    $channel_type = $_POST["channel_type"];
    $csno = $_POST["csno"] ?? ''; // CS No. (Ref) field

    //var_dump($_POST);
    //exit();

    // Validate input
    if (empty($issue_details) || empty($priority) || empty($assigned_worker)) {
        $errors[] = "All fields are required.";
    }

    if (empty($errors)) {
        try {
            $pdo->beginTransaction();

            // Update ticket
            $updated_by = $_SESSION['userdata']['username'];
            $update_query = "UPDATE tickets SET 
                customer_number = :customer_number,
                contact_name = :contact_name,
                issue_details = :issue_details,
                priority = :priority,
                assigned_worker = :assigned_worker,
                status = :status,
                ticket_type = :ticket_type,
                affecting_service = :affecting_service,
                symptoms_details = :symptoms_details,
                product_type = :product_type,
                severity = :severity,
                channel_type = :channel_type,
                updated_at = NOW(),
                updated_by = :updated_by,
                csno = :csno,
                open_at = :open_at
                WHERE ticket_number = :ticket_number";

            $update_stmt = $pdo->prepare($update_query);
            $update_stmt->execute([
                ':customer_number' => $customer_number,
                ':contact_name' => $contact_name,
                ':issue_details' => $issue_details,
                ':priority' => $priority,
                ':assigned_worker' => $assigned_worker,
                ':status' => $status,
                ':ticket_type' => $ticket_type,
                ':affecting_service' => $affecting_service,
                ':symptoms_details' => $symptoms_details,
                ':product_type' => $product_type,
                ':severity' => $severity,
                ':channel_type' => $channel_type,
                ':ticket_number' => $ticket_id,
                ':updated_by' => $updated_by,
                ':csno' => $csno,
                ':open_at' => $open_at
            ]);

            // Record status change if status was updated
            if ($status !== $ticket['status']) {
                $history_query = "INSERT INTO ticket_status_history 
                    (ticket_number, status, changed_by, comments) 
                    VALUES (:ticket_number, :status, :changed_by, :comments)";
                $history_stmt = $pdo->prepare($history_query);
                $history_stmt->execute([
                    ':ticket_number' => $ticket_id,
                    ':status' => $status,
                    ':changed_by' => $_SESSION['user_id'],
                    ':comments' => "Status updated during ticket edit"
                ]);
            }
            $to_open_at = date('Y-m-d H:i:s', strtotime($open_at));

            if ($to_open_at !== $ticket['open_at']) {
                $history_query = "INSERT INTO ticket_status_history 
                    (ticket_number, status, changed_by, comments) 
                    VALUES (:ticket_number, :status, :changed_by, :comments)";
                $history_stmt = $pdo->prepare($history_query);
                $history_stmt->execute([
                    ':ticket_number' => $ticket_id,
                    ':status' => $status,
                    ':changed_by' => $_SESSION['user_id'],
                    ':comments' => "Open time updated(open_at was changed from " . $ticket['open_at'] . " to " . $to_open_at . ")"
                ]);
            }

            $pdo->commit();
            header("Location: ticket.php?tkt_id=" . $ticket_id . "&success=Ticket updated successfully");
            exit();
        } catch (Exception $e) {
            $pdo->rollBack();
            $errors[] = "Error updating ticket: " . $e->getMessage();
        }
    }
}

require_once '../../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Edit Ticket #<?php echo htmlspecialchars($ticket_id); ?></h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <?php foreach ($errors as $error): ?>
                                <p class="mb-0"><?php echo htmlspecialchars($error); ?></p>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST">
                        <div class="row">
                            <!-- Left Column -->
                            <div class="col-md-6">
                                <div class="card border-info mb-3">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-info-circle"></i> Basic Information
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- Existing Basic Information fields -->
                                        <div class="form-group">
                                            <label>CS No.(Ref):</label>
                                            <input type="text" id="csno" name="csno" class="form-control" value="<?php echo htmlspecialchars($ticket['csno'] ?? ''); ?>" readonly>
                                            <label>Contact Person:</label>
                                            <input type="text" id="customer_contact" name="customer_contact" class="form-control" value="<?php echo htmlspecialchars($ticket['Customer_contact'] ?? ''); ?>" readonly>
                                            <label>Site Name:</label>
                                            <input type="text" id="sitename" name="sitename" class="form-control" value="<?php echo htmlspecialchars($ticket['Site_Name'] ?? ''); ?>" readonly>
                                        </div>

                                        <div class="form-group">
                                            <label for="customer_name">Customer Name:</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="customer_search"
                                                    value="<?php echo htmlspecialchars($ticket['customer_name'] ?? ''); ?>"
                                                    placeholder="Search customer...">
                                                <div class="input-group-append">
                                                    <button class="btn btn-outline-secondary" type="button" id="searchCustomer">
                                                        <i class="fas fa-search"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div id="customerResults" class="list-group mt-2 d-none"></div>
                                            <input type="hidden" name="customer_number" id="customer_number"
                                                value="<?php echo htmlspecialchars($ticket['customer_number']); ?>" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="ticket_contact_name">Ticket Contact Name:</label>
                                            <input type="text" class="form-control" name="ticket_contact_name" id="ticket_contact_name"
                                                value="<?php echo htmlspecialchars($ticket['contact_name'] ?? '-'); ?>" placeholder="Who Request, Contact Name">
                                        </div>
                                        <div class="form-group">
                                            <label for="issue_details">Issue Details:</label>
                                            <textarea class="form-control" name="issue_details" id="issue_details" style="height: 330px;"
                                                rows="5" required><?php echo htmlspecialchars($ticket['issue_details'] ?? '-'); ?></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column -->
                            <div class="col-md-6">
                                <div class="card border-primary mb-3">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-ticket-alt"></i> Ticket Details
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- Existing Ticket Details fields -->
                                        <div class="form-group">
                                            <label>Ticket Type:</label>
                                            <select name="ticket_type" class="form-control" required>
                                                <option value="">Select Type</option>
                                                <?php foreach ($ticket_type as $ttype): ?>
                                                    <option value="<?php echo htmlspecialchars($ttype['id']); ?>"
                                                        <?php echo ($ticket['ticket_type'] == $ttype['id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($ttype['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label>Service Impact:</label>
                                            <select name="affecting_service" class="form-control" required>
                                                <option value="">Select Impact</option>
                                                <option value="1" <?php echo ($ticket['affecting_service'] == "1") ? 'selected' : ''; ?>>กระทบ</option>
                                                <option value="2" <?php echo ($ticket['affecting_service'] == "2") ? 'selected' : ''; ?>>ไม่กระทบ</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label>Symptoms:</label>
                                            <select name="Symptomsdetails" class="form-control" required>
                                                <option value="">Select Symptoms</option>
                                                <?php foreach ($symptoms as $symptom): ?>
                                                    <option value="<?php echo htmlspecialchars($symptom['code']); ?>"
                                                        <?php echo ($ticket['symptoms_details'] == $symptom['code']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($symptom['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="form-group d-none">
                                            <label>Product Type:</label>
                                            <select name="product_type" class="form-control" required>
                                                <option value="">Select Product Type</option>
                                                <?php foreach ($product_types as $type): ?>
                                                    <option value="<?php echo htmlspecialchars($type['code']); ?>"
                                                        <?php echo ($ticket['product_type'] == $type['code']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($type['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label>Severity:</label>
                                            <select name="severity" class="form-control" required>
                                                <option value="">Select Severity</option>
                                                <option value="S1" <?php echo ($ticket['severity'] == "S1") ? 'selected' : ''; ?>>Severity 1 (S1- Service Critical Impact)</option>
                                                <option value="S2" <?php echo ($ticket['severity'] == "S2") ? 'selected' : ''; ?>>Severity 2 (S2- Significant Impact)</option>
                                                <option value="S3" <?php echo ($ticket['severity'] == "S3") ? 'selected' : ''; ?>>Severity 3 (S3- Minor impact)</option>
                                                <option value="S4" <?php echo ($ticket['severity'] == "S4") ? 'selected' : ''; ?>>Severity 4 (S4- Low Impact)</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="priority">Priority:</label>
                                            <select class="form-control" name="priority" id="priority" required>
                                                <option value="">Select Priority</option>
                                                <option value="High" <?php echo ($ticket['priority'] == "High") ? 'selected' : ''; ?>>High</option>
                                                <option value="Medium" <?php echo ($ticket['priority'] == "Medium") ? 'selected' : ''; ?>>Medium</option>
                                                <option value="Low" <?php echo ($ticket['priority'] == "Low") ? 'selected' : ''; ?>>Low</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="status">Status:</label>
                                            <select class="form-control" name="status" id="status" required>
                                                <option value="Open" <?php echo ($ticket['status'] == "Open") ? 'selected' : ''; ?>>Open</option>
                                                <option value="In Progress" <?php echo ($ticket['status'] == "In Progress") ? 'selected' : ''; ?>>In Progress</option>
                                                <option value="Pending" <?php echo ($ticket['status'] == "Pending") ? 'selected' : ''; ?>>On Hold(Stop Clock)</option>
                                                <option value="Closed" <?php echo ($ticket['status'] == "Closed") ? 'selected' : ''; ?>>Closed</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="assigned_worker">Assigned Staff:</label>
                                            <select class="form-control" name="assigned_worker" id="assigned_worker" required>
                                                <option value="">Select Worker</option>
                                                <?php foreach ($workers as $worker): ?>
                                                    <option value="<?php echo htmlspecialchars($worker['username']); ?>"
                                                        <?php echo ($ticket['assigned_worker'] == $worker['username']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($worker['fullname']) . '  (' . $worker['department'] . ')'; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>Open At :</label>
                                            <div class="input-group">
                                                <div class="input-group-append">
                                                    <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                                </div>
                                                <input type="text" class="form-control" id="open_at" name="open_at" value="<?= $ticket['open_at'] ?? date('Y-m-d H:i:s'); ?>" required />
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label>Channel Type:</label>
                                            <select name="channel_type" class="form-control" required>
                                                <option value="">Select Channel</option>

                                                <?php foreach ($channel_types as $type): ?>
                                                    <option value="<?php echo htmlspecialchars($type['id']); ?>"
                                                        <?php echo ($ticket['channel_type'] == $type['id']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($type['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>

                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group text-right mt-3">
                            <a href="ticket.php?tkt_id=<?php echo urlencode($ticket_id); ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../../includes/main_script_loader.php'; ?>

<script>
    $(document).ready(function() {
        let searchTimeout;
        $('#open_at').datetimepicker({
            dateFormat: 'yy-mm-dd',
            timeFormat: 'HH:mm:ss',
            showButtonPanel: true,
            currentText: 'Now'
        });

        function performSearch(searchTerm) {
            if (searchTerm.length < 2) {
                $('#customerResults').addClass('d-none').html('');
                return;
            }

            $.ajax({
                url: '<?= BASE_URL ?>/api/search_customers.php',
                method: 'GET',
                data: {
                    term: searchTerm
                },
                success: function(response) {
                    let html = '';
                    response.forEach(customer => {
                        html += `<a href="#" class="list-group-item list-group-item-action" 
                              data-id="${customer.CusCode}" data-csno="${customer.Ref}" data-contact="${customer.Customer_contact}" data-sitename="${customer.Site_Name}">
                            ${customer.CusName} (${customer.CusCode}) Ref: ${customer.Ref} Login: ${customer.Login}
                           </a>`;
                    });
                    $('#customerResults').html(html).removeClass('d-none');
                },
                error: function(xhr, status, error) {
                    $('#customerResults').html('<div class="list-group-item text-danger">Error searching customers</div>').removeClass('d-none');
                }
            });
        }

        $('#customer_search').on('keyup', function() {
            clearTimeout(searchTimeout);
            const searchTerm = $(this).val();
            searchTimeout = setTimeout(function() {
                performSearch(searchTerm);
            }, 300);
        });

        $('#searchCustomer').on('click', function() {
            const searchTerm = $('#customer_search').val();
            performSearch(searchTerm);
        });

        $(document).on('click', '#customerResults a', function(e) {
            e.preventDefault();
            const customerId = $(this).data('id');
            const csno = $(this).data('csno');
            const customerName = $(this).text().split('(')[0].trim();

            $('#customer_search').val(customerName);
            $('#customer_number').val(customerId);
            $('#csno').val(csno);
            $('#customer_contact').val($(this).data('contact'));
            $('#sitename').val($(this).data('sitename'));
            $('#customerResults').addClass('d-none');
        });

        $(document).on('click', function(e) {
            if (!$(e.target).closest('.form-group').length) {
                $('#customerResults').addClass('d-none');
            }
        });
    });
</script>
<?php require_once '../../includes/footer.php'; ?>
<?php
session_start();
header('Content-Type: application/json');

require_once '../../config/defined.conf.php';
require_once '../../config/database.php';
require_once '../../vendor/autoload.php';
use <PERSON><PERSON>Mailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

require_once '../../includes/functions.php';


if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Not authorized']);
    exit;
}

try {
    $ticket_numbers = json_decode($_POST['ticket_numbers'], true);
    $new_worker = trim($_POST['new_worker']);
    $transfer_reason = trim($_POST['transfer_reason']);
    
    if (empty($ticket_numbers) || empty($new_worker) || empty($transfer_reason)) {
        throw new Exception("All fields are required");
    }

    $pdo->beginTransaction();

    foreach ($ticket_numbers as $ticket_number) {
        // Get current worker
        $ticket_query = "SELECT tk.ticket_number,tk.assigned_worker,c.CusName AS customer_name
        FROM 	tickets tk
            LEFT JOIN KCS_DB.Customers c ON tk.customer_number = c.CusCode
        WHERE 	ticket_number = ?";

        $stmt = $pdo->prepare($ticket_query);
        $stmt->execute([$ticket_number]);
        $ticket = $stmt->fetch(PDO::FETCH_ASSOC);

        $current_worker = $ticket['assigned_worker']??'';
        if (!$current_worker) {
            $current_worker = 'Unassigned';
        }

        // Update ticket assignment
        $update_query = "UPDATE tickets SET 
                        assigned_worker = ?,
                        updated_at = CURRENT_TIMESTAMP,
                        updated_by = ?
                        WHERE ticket_number = ?";
        $update_stmt = $pdo->prepare($update_query);
        $update_stmt->execute([$new_worker, $_SESSION['user_id'], $ticket_number]);

        $forward_query = "INSERT INTO ticket_forwards 
                        (ticket_number, from_worker, to_worker, forwarded_by, notes, created_at) 
                        VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)";
        $forward_stmt = $pdo->prepare($forward_query);
        $forward_stmt->execute([
            $ticket_number,
            $current_worker,
            $new_worker,
            $_SESSION['user_id'],
            $transfer_reason
        ]);

        // Add system comment
        $comment = "<div class='alert alert-info'>
            <strong>Ticket Transferred</strong><br>
            Transferred from <strong>{$current_worker}</strong> to <strong>{$new_worker}</strong><br>
            <strong>Reason:</strong> " . nl2br(htmlspecialchars($transfer_reason)) . "
        </div>";

        $comment_query = "INSERT INTO ticket_comments 
                         (ticket_number, user_id, comment, created_at, is_system)
                         VALUES (?, ?, ?, CURRENT_TIMESTAMP, 1)";
        $comment_stmt = $pdo->prepare($comment_query);
        $comment_stmt->execute([$ticket_number, $_SESSION['user_id'], $comment]);

//Send notification to the worker
        $worker_email_query = "SELECT email FROM users WHERE username = ?";
        $worker_email_stmt = $pdo->prepare($worker_email_query);
        $worker_email_stmt->execute([$new_worker]);
        $worker_email = $worker_email_stmt->fetchColumn();
        $BASE_URL = BASE_URL;
        if ($worker_email) {
            $subject = "Ticket #{$ticket_number} has been Transferred to you";
            $message = "<h4>A ticket has been Transferred to you.</h4><br>";
            $message .= "<b>Ticket :</b> #{$ticket_number}<br>";
            $message .= "<b>Customer :</b> {$ticket['customer_name']}<br>";
            $message .= "<b>Notes :</b> {$transfer_reason}<br><br>";
            $message .= "Please review this ticket at : <a href='{$BASE_URL}/modules/tickets/ticket.php?tkt_id={$ticket_number}'>{$ticket_number}</a> ";

            // Uncomment when you have an email function
            sendNotifyEmail($worker_email, $subject, $message,'<EMAIL>');
        }

        
    }

    $pdo->commit();
    echo json_encode(['success' => true]);

} catch (Exception $e) {
    $pdo->rollBack();
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
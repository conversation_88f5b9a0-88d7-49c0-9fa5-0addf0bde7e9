<?php
session_start();
require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST' || !isset($_POST['action']) || $_POST['action'] !== 'send_email') {
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
    exit;
}

$ticket_id = $_POST['ticket_id'] ?? '';
$email_to = $_POST['email_to'] ?? '';
$email_cc = $_POST['email_cc'] ?? '';
$email_subject = $_POST['email_subject'] ?? '';
$email_body = $_POST['email_body'] ?? '';
$include_ticket_details = isset($_POST['include_ticket_details']);

if (empty($ticket_id) || empty($email_to) || empty($email_subject) || empty($email_body)) {
    echo json_encode(['success' => false, 'message' => 'All required fields must be filled']);
    exit;
}

try {
    // Get ticket details
    $ticket_query = "SELECT tk.*,c.CusName AS customer_name
        FROM 	tickets tk
            LEFT JOIN KCS_DB.Customers c ON tk.customer_number = c.CusCode
        WHERE 	ticket_number = ?";
    $stmt = $pdo->prepare($ticket_query);

    $stmt->execute([$ticket_id]);
    $ticket = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$ticket) {
        echo json_encode(['success' => false, 'message' => 'Ticket not found']);
        exit;
    }
    
    // Prepare email body
    $final_body = nl2br(htmlspecialchars($email_body));
    
    if ($include_ticket_details) {
        $ticket_details = "
        <hr>
        <h4>Ticket Details:</h4>
        <table style='border-collapse: collapse; width: 100%;'>
            <tr><td style='padding: 5px; border: 1px solid #ddd;'><strong>Ticket Number:</strong></td><td style='padding: 5px; border: 1px solid #ddd;'>{$ticket['ticket_number']}</td></tr>
            <tr><td style='padding: 5px; border: 1px solid #ddd;'><strong>Service Number:</strong></td><td style='padding: 5px; border: 1px solid #ddd;'>{$ticket['csno']}</td></tr>
            <tr><td style='padding: 5px; border: 1px solid #ddd;'><strong>Customer:</strong></td><td style='padding: 5px; border: 1px solid #ddd;'>{$ticket['customer_name']}</td></tr>
            <tr><td style='padding: 5px; border: 1px solid #ddd;'><strong>Status:</strong></td><td style='padding: 5px; border: 1px solid #ddd;'>{$ticket['status']}</td></tr>
            <tr><td style='padding: 5px; border: 1px solid #ddd;'><strong>Priority:</strong></td><td style='padding: 5px; border: 1px solid #ddd;'>{$ticket['priority']}</td></tr>
            <tr><td style='padding: 5px; border: 1px solid #ddd;'><strong>Created:</strong></td><td style='padding: 5px; border: 1px solid #ddd;'>{$ticket['created_at']}</td></tr>
        </table>";
        $final_body .= $ticket_details;
    }
    
    // Add signature
    $final_body .= "
    <br><br>
    <p><strong>Best Regards,</strong><br>
    <strong>Customer Support Department</strong><br>
    <em>Tel. : 1605, +************</em><br>
    <em>Email : <a href='mailto:<EMAIL>'><EMAIL></a></em></p>";
    
    // Send email
    $recipients = $email_to;
    if (!empty($email_cc)) {
        $recipients .= ',' . $email_cc;
    }
    
    $email_sent = sendNotifyEmail($recipients, $email_subject, $final_body,'<EMAIL>');
    
    if ($email_sent) {
        // Log the email activity
        $comment = "Email sent by " . $_SESSION['userdata']['username'] . " to: " . $email_to;
        if (!empty($email_cc)) {
            $comment .= " (CC: " . $email_cc . ")";
        }
        $comment .= "<br>Subject: " . htmlspecialchars($email_subject);
        
        $insert_comment = "INSERT INTO ticket_comments (ticket_number, user_id, comment_base64, created_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP)";
        $stmt = $pdo->prepare($insert_comment);
        $stmt->execute([$ticket_id, $_SESSION['user_id'], base64_encode($comment)]);
        
        echo json_encode(['success' => true, 'message' => 'Email sent successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to send email']);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
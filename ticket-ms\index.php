<?php
session_start();
// Set error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once 'config/defined.conf.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

// Check if user is not logged in
if (!isset($_SESSION['user_id']) || !isset($_SESSION['userdata'])) {
    $_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
    header("Location: " . BASE_URL . "/modules/auth/login.php");
    exit();
}

$workername = $_SESSION['userdata']['username'] ?? '';
$selected_staff = $_GET['staff'] ?? $workername;
// Fetch assigned tickets for user's team
$query = "SELECT tk.*,c.<PERSON>us<PERSON>ame AS customer_name,m.Site_Address,m.Login AS login_name FROM 
(
SELECT t.ticket_number,
       t.customer_number,
       t.issue_details,
       t.priority,
       t.status,
       t.created_at,
       u.fullname as assigned_worker_name,
       t.username AS created_by,
       t.csno,t.open_at,t.closed_at
FROM tickets t LEFT JOIN users u ON t.assigned_worker = u.username 
WHERE t.assigned_worker = :user_id
  AND t.status <> 'Closed'
ORDER BY t.created_at DESC
)AS tk
LEFT JOIN `KCS_DB`.`Main` `m` ON(`tk`.`csno` = `m`.Ref)
LEFT JOIN KCS_DB.`Customers` `c` ON (tk.customer_number = c.CusCode)";

$stmt = $pdo->prepare($query);
$stmt->bindParam(':user_id', $selected_staff);
$stmt->execute();
$assigned_tickets = $stmt->fetchAll(PDO::FETCH_ASSOC);

require_once 'includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10 mx-auto">
            <h2 class="mb-4 text-center">Welcome to the Ticket Management System</h2>

            <div class="card shadow">
                <div class="card-header bg-primary  d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center" id="staffSelectContainer">
                        <h5 class="mb-0 mr-3 text-white">Your's Assigned Tickets</h5>
                        <?php if ($_SESSION['userdata']['role'] === 'admin'): ?>
                            <div class="form-group">
                            <form method="GET" class="form-inline">
                                <select name="staff" id="staffSelect" class="form-control form-control-sm" onchange="this.form.submit()">
                                    <?php
                                    $staff_query = "SELECT * 
                                                  FROM users 
                                                  WHERE status = 'active' AND division='Operation'
                                                  ORDER BY fullname";
                                    $staff_result = $pdo->query($staff_query);
                                    while ($staff = $staff_result->fetch()) {
                                        $selected = ($staff['username'] === $selected_staff) ? 'selected' : '';
                                        echo '<option value="' . htmlspecialchars($staff['username']) . '" ' . $selected . '>' . 
                                             htmlspecialchars($staff['fullname']??'') . '  ('.$staff['department'].')</option>';
                                    }
                                    ?>
                                </select>
                            </form>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div>
                        <button type="button" id="transferSelectedBtn" class="btn btn-light btn-sm mr-2" disabled>
                            <i class="fas fa-exchange-alt"></i> Transfer Selected
                        </button>
                        <span class="badge badge-light">Total: <?php echo count($assigned_tickets); ?></span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover small">
                            <thead>
                                <tr>
                                    <th>
                                        <input type="checkbox" id="selectAll">
                                    </th>
                                    <th>Ticket No.</th>
                                    <th>Customer</th>
                                    <th>CS No.(Ref)</th>
                                    <th>Login</th>
                                    <th>Site Address</th>
                                    <th>Issue</th>
                                    <th>Priority</th>
                                    <th>Status</th>
                                    <th>Created At</th>
                                    <th>Open At</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($assigned_tickets)): ?>
                                    <?php foreach ($assigned_tickets as $ticket): ?>
                                        <tr class="<?php echo getTicketRowClass($ticket['priority'], $ticket['status']); ?>">
                                            <td>
                                                <input type="checkbox" class="ticket-checkbox" 
                                                       value="<?php echo $ticket['ticket_number']; ?>">
                                            </td>
                                            <td>
                                                <a target="_blank" href="<?= BASE_URL ?>/modules/tickets/ticket.php?tkt_id=<?php echo urlencode($ticket['ticket_number'] ?? ''); ?>" title="View Ticket">
                                                    <?php echo $ticket['ticket_number'] ?? 'N/A'; ?>
                                                </a>
                                            </td>
                                            <td>
                                                <?php echo $ticket['customer_name'] ?? 'N/A'; ?>
                                                <small class="d-block text-muted"><?php echo $ticket['customer_number'] ?? 'N/A'; ?></small>
                                            </td>
                                            <td><?php echo $ticket['csno'] ?? 'N/A'; ?></td>
                                            <td>
                                                <?php echo $ticket['login_name'] ?? ''; ?>
                                            </td>
                                            <td><?php echo $ticket['Site_Address']??''?></td>
                                            <td><?php echo text_to_readmore($ticket['issue_details'] ?? 'No details'); ?></td>
                                            <td>
                                                <span class="badge badge-<?php echo getPriorityBadgeClass($ticket['priority']); ?>">
                                                    <?php echo $ticket['priority'] ?? 'N/A'; ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?php echo getStatusBadgeClass($ticket['status']); ?>">
                                                    <?php 
                                                    $status = $ticket['status'] ?? '';
                                                    if($status === 'Pending') {
                                                        echo 'On Hold(Stop Clock)';
                                                    } else {
                                                        echo htmlspecialchars($status);
                                                    }                                                 
                                                    ?>
                                                </span>
                                            </td>
                                            <td><?php echo $ticket['created_at']??'-'; ?></td>
                                            <td>
                                                <?php echo $ticket['open_at']??'-'; ?>
                                            </td>
                                            <td>
                                                <a target="_blank" href="<?= BASE_URL ?>/modules/tickets/ticket.php?tkt_id=<?php echo urlencode($ticket['ticket_number'] ?? ''); ?>"
                                                    class="btn btn-sm btn-info" title="View Ticket">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="text-center text-muted">No tickets assigned to your team.</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Add this before the closing </body> tag -->
<div class="modal fade" id="transferModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title">Transfer Selected Tickets</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="transferForm" method="POST" action="modules/tickets/transfer_tickets.php">
                <div class="modal-body">
                    <input type="hidden" name="ticket_numbers" id="ticketNumbers">
                    
                    <div class="form-group">
                        <label>Transfer to Staff:</label>
                        <select name="new_worker" id="newWorkerSelect"  class="form-control" required>
                            <option value="">Select Staff</option>
                            <?php
                            $staff_query = "SELECT *
                                          FROM users 
                                          WHERE status = 'active' AND division='Operation'
                                          ORDER BY fullname";
                            $staff_result = $pdo->query($staff_query);
                            while ($staff = $staff_result->fetch()) {
                                echo '<option value="'.htmlspecialchars($staff['username']??'').'">' . htmlspecialchars($staff['fullname']??'') . '  ('.$staff['department'].')</option>';
                            }
                            ?>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label>Reason for Transfer:</label>
                        <textarea name="transfer_reason" class="form-control" rows="3" required
                                placeholder="Explain why you're transferring these tickets..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info btn-transfer">Transfer Tickets</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php
require_once 'includes/main_script_loader.php';
?>
<script>
    // Auto reload page every 3 minutes
    setTimeout(function() {
        window.location.reload();
    }, 60000*3);

    // Optional: Add countdown timer to show time until next refresh
    let timeLeft = 180;
    setInterval(function() {
        timeLeft--;
        if (timeLeft >= 0) {
            document.title = ` (${timeLeft}s)`+'<?= WEBTITLE ?>';
        }
    }, 1000);

    $(document).ready(function() {
        // Initialize Select2 for transfer modal dropdown
        $('#newWorkerSelect').select2({
            theme: 'bootstrap4',
            placeholder: 'Search and select staff...',
            allowClear: true,
            width: '100%',
            dropdownParent: $('#transferModal')
        });
        $('#staffSelect').select2({
            theme: 'bootstrap4',
            placeholder: 'Search and select staff...',
            allowClear: true,
            width: '100%',
            dropdownParent: $('#staffSelectContainer')
        });

        const selectAll = $('#selectAll');
        const ticketCheckboxes = $('.ticket-checkbox');
        const transferBtn = $('#transferSelectedBtn');

        // Handle "Select All" checkbox
        selectAll.change(function() {
            ticketCheckboxes.prop('checked', this.checked);
            updateTransferButton();
        });

        // Handle individual checkboxes
        ticketCheckboxes.change(function() {
            updateTransferButton();
            // Update "Select All" if needed
            selectAll.prop('checked', 
                ticketCheckboxes.length === ticketCheckboxes.filter(':checked').length);
        });

        // Update transfer button state
        function updateTransferButton() {
            const selectedCount = ticketCheckboxes.filter(':checked').length;
            transferBtn.prop('disabled', selectedCount === 0);
            transferBtn.html(`<i class="fas fa-exchange-alt"></i> Transfer Selected (${selectedCount})`);
        }

        // Handle transfer button click
        transferBtn.click(function() {
            const selectedTickets = [];
            ticketCheckboxes.filter(':checked').each(function() {
                selectedTickets.push($(this).val());
            });
            
            $('#ticketNumbers').val(JSON.stringify(selectedTickets));
            $('#transferModal').modal('show');
        });

        // Handle form submission
        $('#transferForm').submit(function(e) {
            e.preventDefault();
            const btntraffer = $('.btn-transfer');
            const originalText = btntraffer.html();
            btntraffer.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Transferring...');
            $.ajax({
                url: $(this).attr('action'),
                method: 'POST',
                data: $(this).serialize(),
                success: function(response) {
                    if (response.success) {
                        toastr.success('Tickets transferred successfully!');
                        setTimeout(() => { location.reload();}, 3000);
                    } else {
                        toastr.error('Error: ' + response.message);
                        btntraffer.prop('disabled', false).html(originalText);
                    }
                },
                error: function() {
                    toastr.error('Error occurred during transfer');
                    btntraffer.prop('disabled', false).html(originalText);
                }
            });
        });
    });
</script>
<?php
require_once 'includes/footer.php';
?>


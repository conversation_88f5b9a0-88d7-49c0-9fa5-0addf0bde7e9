<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
//error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Initialize variables
$customer_number = "";
$issue_details = "";
$priority = "";
$assigned_team = "";
$errors = [];

// Get teams for dropdown
$teams_query = "SELECT id, name FROM teams ORDER BY name";
$teams_result = $pdo->query($teams_query);
$teams = $teams_result->fetchAll(PDO::FETCH_ASSOC);

// Add this after your existing database queries
$symptoms_query = "SELECT id, code, name FROM symptoms WHERE is_active = 1 ORDER BY name";
$symptoms_result = $pdo->query($symptoms_query);
$symptoms = $symptoms_result->fetchAll(PDO::FETCH_ASSOC);

// Get product types for dropdown
$product_types_query = "SELECT id, code, name FROM product_types WHERE is_active = 1 ORDER BY name";
$product_types_result = $pdo->query($product_types_query);
$product_types = $product_types_result->fetchAll(PDO::FETCH_ASSOC);

// Get channel types for dropdown
$channel_types_query = "SELECT id, name FROM channel_types WHERE is_active = 1 ORDER BY name";
$channel_types_result = $pdo->query($channel_types_query);
$channel_types = $channel_types_result->fetchAll(PDO::FETCH_ASSOC);
// get ticket type
$ticket_type_query = "SELECT id, name FROM ticket_type WHERE is_active = 1 ORDER BY name";
$ticket_type_result = $pdo->query($ticket_type_query);
$ticket_type = $ticket_type_result->fetchAll(PDO::FETCH_ASSOC);

// Get workers for dropdown
$workers_query = "SELECT * FROM users 
                 WHERE status='active' AND division='Operation'
                 ORDER BY fullname";
$workers_result = $pdo->query($workers_query);
$workers = $workers_result->fetchAll(PDO::FETCH_ASSOC);

$symptoms_details = '';
$product_type = '';
$channel_type = '';

// Handle form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    //var_dump($_POST);
    //exit;

    $customer_number = trim($_POST["customer_number"]);
    $contact_name = trim($_POST["ticket_contact_name"] ?? '');
    $issue_details = trim($_POST["issue_details"]);
    $priority = trim($_POST["priority"]);
    $tmp_username = $_SESSION['userdata']['username'] ?? '';
    $assigned_worker = trim($_POST["assigned_worker"] ?? $tmp_username);
    $open_at = trim($_POST['open_at'] ?? date('Y-m-d H:i:s')); // Default to current time if not provided

    $ticket_type = trim($_POST['ticket_type']);
    $affecting_service = trim($_POST['affecting_service']);
    $symptoms_details = trim($_POST['Symptomsdetails'] ?? '');
    $product_type = trim($_POST['product_type'] ?? '');
    $severity = trim($_POST['severity']);
    $channel_type = trim($_POST['channel_type'] ?? '');
    $csno = trim($_POST['csno'] ?? '');

    $errors = array();

    //var_dump($customer_number, $issue_details, $priority, $assigned_worker,  $channel_type);    
    // Validate input
    if (empty($issue_details) || empty($priority) || empty($assigned_worker) || empty($channel_type)) {
        $errors[] = "All fields are required.";
    }
    if (empty($csno)) {
        $errors[] = "CS No.(Ref) is required.";
    }

    // If no errors, insert ticket into database
    if (empty($errors)) {
        $next_ticket_number = generateTicketNumber($pdo);
        $query = "INSERT INTO tickets (
            ticket_number, customer_number, 
            issue_details, priority, 
             username, status, created_at,
            ticket_type, affecting_service, 
            symptoms_details, severity, 
            channel_type,csno,open_at,
            assigned_worker,
            contact_name
        ) VALUES (?, ?, ?, ?, ?,'Open', NOW(), ?, ?, ?, ?, ?, ?, ?, ?,?)";

        $stmt = $pdo->prepare($query);
        $isdone = false;
        $isdone = $stmt->execute([
            $next_ticket_number,
            $customer_number,
            $issue_details,
            $priority,
            $_SESSION['userdata']['username'],
            $ticket_type,
            $affecting_service,
            $symptoms_details,
            $severity,
            $channel_type,
            $csno,
            $open_at,
            $assigned_worker,
            $contact_name
        ]);

        if ($isdone) {

            header("Location: ticket.php?tkt_id=" . htmlspecialchars($next_ticket_number) . "&success=Ticket created successfully.");
            exit();
        } else {
            $errors[] = "Error creating ticket. Please try again.";
        }
    } else {
        // If there are errors, display them
        foreach ($errors as $error) {
            echo "<div class='alert alert-danger'>$error <br>Please try again!</div>";
        }
    }
}
require_once '../../includes/header.php';

?>
<div class="container-fluid mt-4">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Create New Ticket</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <?php foreach ($errors as $error): ?>
                                <p class="mb-0"><?php echo $error; ?></p>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>

                    <form action="create.php" method="POST">
                        <div class="row">
                            <!-- Left Column -->
                            <div class="col-md-6">
                                <div class="card border-info mb-3">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-info-circle"></i> Basic Information
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- Move existing Basic Information fields here -->
                                        <div class="form-group">
                                            <label>CS No.(Ref):</label>
                                            <input type="text" id="csno" name="csno" class="form-control" value="" readonly>
                                            <label>Contact Person:</label>
                                            <input type="text" id="customer_contact" name="customer_contact" class="form-control" value="" readonly>
                                            <label>Site Name:</label>
                                            <input type="text" id="sitename" name="sitename" class="form-control" value="" readonly>
                                        </div>

                                        <div class="form-group">
                                            <label for="customer_name">Customer Name:</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="customer_search" placeholder="Search customer..." title="Customer ID | Customer Name | Ref | Login"
                                                    value="<?php echo htmlspecialchars($customer_number); ?>" required>
                                                <div class="input-group-append">
                                                    <button class="btn btn-outline-secondary" type="button" id="searchCustomer">
                                                        <i class="fas fa-search"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div id="customerResults" class="list-group mt-2 d-none"></div>
                                            <input type="hidden" name="customer_number" id="customer_number" required>
                                        </div>
                                        <div class="form-group">
                                            <label for="ticket_contact_name">Ticket Contact Name:</label>
                                            <input type="text" class="form-control" name="ticket_contact_name" id="ticket_contact_name"
                                                value="" placeholder="Who Request, Contact Name">
                                        </div>
                                        <div class="form-group">
                                            <label for="issue_details">Issue Details:</label>
                                            <textarea class="form-control" name="issue_details" id="issue_details" style="height: 243px;"
                                                rows="5" placeholder="Please enter details" required></textarea>
                                        </div>


                                    </div>
                                </div>
                            </div>

                            <!-- Right Column -->
                            <div class="col-md-6">
                                <div class="card border-primary mb-3">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">
                                            <i class="fas fa-ticket-alt"></i> Ticket Details
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- Move existing Ticket Details fields here -->
                                        <div class="form-group">
                                            <label>Ticket Type:</label>
                                            <select name="ticket_type" id="ticket_type" class="form-control" required>
                                                <option value="">Select Ticket Type</option>
                                                <?php foreach ($ticket_type as $ttype): ?>
                                                    <option value="<?php echo $ttype['id']; ?>"
                                                        <?php echo ($ttype['id'] == 1) ? 'selected' : '';  ?>>
                                                        <?php echo htmlspecialchars($ttype['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="form-group">
                                            <label>Service Impact:</label>
                                            <select name="affecting_service" id="affecting_service" class="form-control" required>
                                                <option value="">Select Impact</option>
                                                <option value="1" selected>กระทบ</option>
                                                <option value="2">ไม่กระทบ</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label>Symptoms:</label>
                                            <select name="Symptomsdetails" id="Symptomsdetails" class="form-control" required>
                                                <option value="">Select Symptoms</option>
                                                <?php foreach ($symptoms as $symptom): ?>
                                                    <option value="<?php echo htmlspecialchars($symptom['code']); ?>"
                                                        <?php echo ($symptom['code'] == 'LINK_DOWN') ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($symptom['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="form-group d-none">
                                            <label>Product Type:</label>
                                            <select name="product_type" id="product_type" class="form-control">
                                                <option value="">Select Product Type</option>
                                                <?php foreach ($product_types as $type): ?>
                                                    <option value="<?php echo htmlspecialchars($type['code']); ?>"
                                                        <?php echo ($type['code'] == 'NETWORK') ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($type['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label>Severity:</label>
                                            <select name="severity" id="severity" class="form-control" required>
                                                <option value="">Select Severity</option>
                                                <option value="S1">Severity 1 (S1- Service Critical Impact)</option>
                                                <option value="S2">Severity 2 (S2- Significant Impact)</option>
                                                <option value="S3" selected>Severity 3 (S3- Minor impact)</option>
                                                <option value="S4">Severity 4 (S4- Low Impact)</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label>Priority:</label>
                                            <select class="form-control" name="priority" id="priority" required>
                                                <option value="">Select Priority</option>
                                                <option value="High">High</option>
                                                <option value="Medium" selected>Medium</option>
                                                <option value="Low">Low</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label>Assigned Staff:</label>
                                            <select class="form-control" name="assigned_worker" id="assigned_worker" required>
                                                <option value="">Select Staff</option>
                                                <?php foreach ($workers as $worker): ?>
                                                    <option value="<?php echo htmlspecialchars($worker['username']); ?>"
                                                        <?php echo ($worker['username'] == $_SESSION['userdata']['username']) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($worker['fullname']) . '  (' . $worker['department'] . ')';; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label>Open At :</label>
                                            <div class="input-group">
                                                <div class="input-group-append">
                                                    <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                                </div>
                                                <input type="text" class="form-control" id="open_at" name="open_at" value="<?= date('Y-m-d H:i:s'); ?>" required />
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label>Channel Type:</label>
                                            <select name="channel_type" id="channel_type" class="form-control" required>
                                                <option value="">Select Channel</option>
                                                <?php foreach ($channel_types as $type): ?>
                                                    <option value="<?php echo htmlspecialchars($type['id']); ?>"
                                                        <?php echo ($type['id'] == 2) ? 'selected' : ''; ?>>
                                                        <?php echo htmlspecialchars($type['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Add this CSS section before the closing </form> tag -->
                        <style>
                            .card {
                                box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
                                transition: all 0.3s ease;
                            }

                            .card:hover {
                                box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
                            }

                            .form-group {
                                margin-bottom: 1rem;
                            }

                            .form-group label {
                                font-weight: 500;
                                color: #495057;
                            }

                            .card-header h5 {
                                font-size: 1.1rem;
                            }

                            .border-info {
                                border-width: 2px !important;
                            }

                            .border-primary {
                                border-width: 2px !important;
                            }

                            .card-header i {
                                margin-right: 0.5rem;
                            }
                        </style>

                        <div class="row mt-4">
                            <div class="col-12 text-right">
                                <a href="list.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Create Ticket
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../../includes/main_script_loader.php';
?>

<script>
    $(document).ready(function() {
        let searchTimeout;
        $('#open_at').datetimepicker({
            dateFormat: 'yy-mm-dd',
            timeFormat: 'HH:mm:ss',
            showButtonPanel: true,
            currentText: 'Now'
        });
        // Function to perform search
        function performSearch(searchTerm) {
            if (searchTerm.length < 2) {
                $('#customerResults').addClass('d-none').html('');
                return;
            }

            $.ajax({
                url: '<?= BASE_URL ?>/api/search_customers.php',
                method: 'GET',
                data: {
                    term: searchTerm
                },
                success: function(response) {
                    //console.log(response);
                    let html = '';
                    response.forEach(customer => {
                        html += `<a href="#" class="list-group-item list-group-item-action" 
                              data-id="${customer.CusCode}" data-csno="${customer.Ref}" data-contact="${customer.Customer_contact}" data-sitename="${customer.Site_Name}">
                            ${customer.CusName} (${customer.CusCode}) Ref: ${customer.Ref} Login: ${customer.Login}
                           </a>`;
                    });
                    $('#customerResults').html(html).removeClass('d-none');
                },
                error: function(xhr, status, error) {
                    console.error("Error fetching customer data:", error);
                    $('#customerResults').html('<div class="list-group-item text-danger">Error searching customers</div>').removeClass('d-none');
                }
            });
        }

        // Search on keyup
        $('#customer_search').on('keyup', function() {
            clearTimeout(searchTimeout);
            const searchTerm = $(this).val();

            searchTimeout = setTimeout(function() {
                performSearch(searchTerm);
            }, 300);
        });

        // Search button click handler
        $('#searchCustomer').on('click', function() {
            const searchTerm = $('#customer_search').val();
            performSearch(searchTerm);
        });

        // Handle customer selection
        $(document).on('click', '#customerResults a', function(e) {
            e.preventDefault();
            const customerId = $(this).data('id');
            const csno = $(this).data('csno');
            const customerName = $(this).text().split('(')[0].trim();

            //console.log($(this).data);

            $('#customer_search').val(customerName);
            $('#customer_number').val(customerId);
            $('#customer_contact').val($(this).data('contact'));
            $('#sitename').val($(this).data('sitename'));
            $('#csno').val(csno);
            $('#customerResults').addClass('d-none');
        });

        // Close results when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.form-group').length) {
                $('#customerResults').addClass('d-none');
            }
        });
    });
</script>
<?php
require_once '../../includes/footer.php';
?>
<?php
session_start();
require_once '../../config/defined.conf.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

$results = [];
// Fetch ticket data
$tickets = getRecentTickets();
$currentYear = date('Y');
$inProgressTickets = countTicketsByStatus('In Progress');
$pendingTickets = countTicketsByStatus('Pending');
$openTickets = countTicketsByStatus('Open');
$closedTickets = countTicketsByStatus('Closed', $currentYear);
$tickets_byAge = getTicketsByAgeRange();

//$results['tickets'] = $tickets;
$results['inProgressTickets'] = $inProgressTickets; 
$results['pendingTickets'] = $pendingTickets;
$results['openTickets'] = $openTickets;
$results['closedTickets'] = $closedTickets;
$results['currentYear'] = $currentYear;
$results['tickets_byAge'] = $tickets_byAge;

header('Content-Type: application/json');
echo json_encode($results);
?>
-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: ************    Database: ticket_management
-- ------------------------------------------------------
-- Server version	5.5.5-10.5.27-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `import_history`
--

DROP TABLE IF EXISTS `import_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `import_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `filename` varchar(255) NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `total_rows` int(11) NOT NULL DEFAULT 0,
  `success_count` int(11) NOT NULL DEFAULT 0,
  `error_count` int(11) NOT NULL DEFAULT 0,
  `import_type` enum('tickets','customers','other') DEFAULT 'tickets',
  `status` enum('pending','processing','completed','failed') DEFAULT 'pending',
  `error_details` text DEFAULT NULL,
  `imported_by` int(11) NOT NULL,
  `started_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `completed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_imported_by` (`imported_by`),
  KEY `idx_status` (`status`),
  KEY `idx_import_type` (`import_type`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `import_history`
--

LOCK TABLES `import_history` WRITE;
/*!40000 ALTER TABLE `import_history` DISABLE KEYS */;
INSERT INTO `import_history` VALUES (40,'202406_.xlsx','202406_.xlsx',958,958,0,'tickets','completed',NULL,41,'2025-06-27 07:28:57','2025-06-27 07:28:58'),(41,'202407_.xlsx','202407_.xlsx',2994,2994,0,'tickets','completed',NULL,41,'2025-06-27 07:32:01','2025-06-27 07:32:03'),(42,'202408_.xlsx','202408_.xlsx',2313,2313,0,'tickets','completed',NULL,41,'2025-06-27 07:44:17','2025-06-27 07:44:18'),(43,'202409_.xlsx','202409_.xlsx',2342,2342,0,'tickets','completed',NULL,41,'2025-06-27 07:48:27','2025-06-27 07:48:28'),(44,'202410_.xlsx','202410_.xlsx',2592,2592,0,'tickets','completed',NULL,41,'2025-06-27 07:49:42','2025-06-27 07:49:43'),(45,'202411_.xlsx','202411_.xlsx',2493,2493,0,'tickets','completed',NULL,41,'2025-06-27 07:51:05','2025-06-27 07:51:06'),(46,'202412_.xlsx','202412_.xlsx',2626,2626,0,'tickets','completed',NULL,41,'2025-06-27 07:51:40','2025-06-27 07:51:42'),(47,'202501_.xlsx','202501_.xlsx',2529,2529,0,'tickets','completed',NULL,41,'2025-06-27 07:53:09','2025-06-27 07:53:10'),(48,'202502_.xlsx','202502_.xlsx',2718,2718,0,'tickets','completed',NULL,41,'2025-06-27 07:53:42','2025-06-27 07:53:44'),(49,'202503_.xlsx','202503_.xlsx',3516,3516,0,'tickets','completed',NULL,41,'2025-06-27 07:54:12','2025-06-27 07:54:14'),(50,'202504_.xlsx','202504_.xlsx',3274,3274,0,'tickets','completed',NULL,41,'2025-06-27 07:54:40','2025-06-27 07:54:42'),(51,'202505_.xlsx','202505_.xlsx',3640,3640,0,'tickets','completed',NULL,41,'2025-06-27 07:55:20','2025-06-27 07:55:22'),(52,'202506_.xlsx','202506_.xlsx',2440,2440,0,'tickets','completed',NULL,41,'2025-06-27 07:55:53','2025-06-27 07:55:54');
/*!40000 ALTER TABLE `import_history` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-18 16:45:00

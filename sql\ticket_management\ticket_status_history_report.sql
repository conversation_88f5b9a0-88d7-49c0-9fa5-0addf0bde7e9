
SELECT CONCAT(tsh1.changed_at, ' - ', IFNULL(tsh2.changed_at, 'Null'), ', ', tsh1.sub_status, ', ', tsh1.comments)AS `stop_clock_event`,
       tsh1.changed_at AS `start`,
       tsh2.changed_at AS `end`,
       tsh1.sub_status,
       tsh1.comments
FROM ticket_status_history tsh1
LEFT JOIN ticket_status_history tsh2 ON tsh1.ticket_number = tsh2.ticket_number
AND tsh2.id =
  (SELECT MIN(id)
   FROM ticket_status_history
   WHERE ticket_number = tsh1.ticket_number
     AND id > tsh1.id )
WHERE tsh1.ticket_number = 'TKT202507-0004'
  AND tsh1.status = 'Pending'
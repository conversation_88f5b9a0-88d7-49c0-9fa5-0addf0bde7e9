-- Database schema extensions for ticket import functionality
-- This script adds tables and fields needed to support importing old ticket data from Excel

USE ticket_management;

-- Create ticket_extended table to store additional fields from imported tickets
CREATE TABLE IF NOT EXISTS ticket_extended (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ticket_number VARCHAR(20) NOT NULL,
    contact_person VARCHAR(255) NULL,
    location VARCHAR(255) NULL,
    effect TEXT NULL,
    project_name VARCHAR(255) NULL,
    owner VA<PERSON><PERSON><PERSON>(255) NULL,
    noc_receiver VARCHAR(255) NULL COMMENT 'NOC (ผู้รับแจ้ง)',
    noc_closer VARCHAR(255) NULL COMMENT 'NOC (ผู้ปิดงาน)',
    main_cause TEXT NULL,
    sub_cause TEXT NULL,
    group_cause1 VARCHAR(255) NULL,
    group_cause2 VARCHAR(255) NULL,
    cause_detail TEXT NULL,
    cause_detail_eng TEXT NULL,
    provider VARCHAR(255) NULL,
    province VARCHAR(255) NULL,
    downtime_hr DECIMAL(10,2) NULL,
    downtime_min DECIMAL(10,2) NULL,
    total_time VARCHAR(255) NULL,
    stop_clock VARCHAR(255) NULL,
    category VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INT NOT NULL,
    updated_at TIMESTAMP NULL,
    updated_by INT NULL,
    FOREIGN KEY (ticket_number) REFERENCES tickets(ticket_number) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_ticket_number (ticket_number),
    INDEX idx_main_cause (main_cause(100)),
    INDEX idx_provider (provider),
    INDEX idx_province (province)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create import_history table to track import operations
CREATE TABLE IF NOT EXISTS import_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    total_rows INT NOT NULL DEFAULT 0,
    success_count INT NOT NULL DEFAULT 0,
    error_count INT NOT NULL DEFAULT 0,
    import_type ENUM('tickets', 'customers', 'other') DEFAULT 'tickets',
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    error_details TEXT NULL,
    imported_by INT NOT NULL,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (imported_by) REFERENCES users(id),
    INDEX idx_imported_by (imported_by),
    INDEX idx_status (status),
    INDEX idx_import_type (import_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create import_errors table to store detailed error information
CREATE TABLE IF NOT EXISTS import_errors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    import_history_id INT NOT NULL,
    row_number INT NOT NULL,
    error_message TEXT NOT NULL,
    row_data JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (import_history_id) REFERENCES import_history(id) ON DELETE CASCADE,
    INDEX idx_import_history (import_history_id),
    INDEX idx_row_number (row_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add additional columns to existing tickets table if they don't exist
-- These are optional fields that might be useful for imported tickets

-- Add closed_at timestamp for better tracking
ALTER TABLE tickets 
ADD COLUMN IF NOT EXISTS closed_at TIMESTAMP NULL AFTER updated_at,
ADD INDEX IF NOT EXISTS idx_closed_at (closed_at);

-- Add import_source to track where the ticket came from
ALTER TABLE tickets 
ADD COLUMN IF NOT EXISTS import_source VARCHAR(50) NULL AFTER closed_at,
ADD INDEX IF NOT EXISTS idx_import_source (import_source);

-- Add original_ticket_id for reference to old system
ALTER TABLE tickets 
ADD COLUMN IF NOT EXISTS original_ticket_id VARCHAR(100) NULL AFTER import_source,
ADD INDEX IF NOT EXISTS idx_original_ticket_id (original_ticket_id);

-- Update ticket_interim table to include more SIM-related fields if needed
ALTER TABLE ticket_interim 
ADD COLUMN IF NOT EXISTS sim_package_detail TEXT NULL AFTER sim_package,
ADD COLUMN IF NOT EXISTS network_info TEXT NULL AFTER sim_package_detail;

-- Create lookup tables for common values to maintain data consistency

-- Providers lookup table
CREATE TABLE IF NOT EXISTS providers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    code VARCHAR(50) NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_code (code),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Provinces lookup table (Thailand provinces)
CREATE TABLE IF NOT EXISTS provinces (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    name_en VARCHAR(255) NULL,
    region VARCHAR(100) NULL,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_name (name),
    INDEX idx_name_en (name_en),
    INDEX idx_region (region),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Cause categories lookup table
CREATE TABLE IF NOT EXISTS cause_categories (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    name_en VARCHAR(255) NULL,
    parent_id INT NULL,
    level TINYINT DEFAULT 1,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES cause_categories(id) ON DELETE SET NULL,
    INDEX idx_name (name),
    INDEX idx_parent (parent_id),
    INDEX idx_level (level),
    INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert some default provinces (major ones)
INSERT IGNORE INTO provinces (name, name_en, region) VALUES
('กรุงเทพมหานคร', 'Bangkok', 'Central'),
('เชียงใหม่', 'Chiang Mai', 'Northern'),
('เชียงราย', 'Chiang Rai', 'Northern'),
('ขอนแก่น', 'Khon Kaen', 'Northeastern'),
('นครราชสีมา', 'Nakhon Ratchasima', 'Northeastern'),
('สงขลา', 'Songkhla', 'Southern'),
('ภูเก็ต', 'Phuket', 'Southern'),
('ระยอง', 'Rayong', 'Eastern'),
('ชลบุรี', 'Chonburi', 'Eastern'),
('นนทบุรี', 'Nonthaburi', 'Central'),
('ปทุมธานี', 'Pathum Thani', 'Central'),
('สมุทรปราการ', 'Samut Prakan', 'Central');

-- Insert some default providers
INSERT IGNORE INTO providers (name, code) VALUES
('AIS', 'AIS'),
('DTAC', 'DTAC'),
('TRUE', 'TRUE'),
('TOT', 'TOT'),
('CAT', 'CAT'),
('3BB', '3BB'),
('NT', 'NT');

-- Insert some default cause categories
INSERT IGNORE INTO cause_categories (name, name_en, level) VALUES
('Hardware', 'Hardware', 1),
('Software', 'Software', 1),
('Network', 'Network', 1),
('Power', 'Power', 1),
('Human Error', 'Human Error', 1),
('External', 'External', 1);

-- Insert sub-categories for Hardware
INSERT IGNORE INTO cause_categories (name, name_en, parent_id, level) 
SELECT 'Server Failure', 'Server Failure', id, 2 FROM cause_categories WHERE name = 'Hardware' LIMIT 1;

INSERT IGNORE INTO cause_categories (name, name_en, parent_id, level) 
SELECT 'Disk Failure', 'Disk Failure', id, 2 FROM cause_categories WHERE name = 'Hardware' LIMIT 1;

INSERT IGNORE INTO cause_categories (name, name_en, parent_id, level) 
SELECT 'Memory Failure', 'Memory Failure', id, 2 FROM cause_categories WHERE name = 'Hardware' LIMIT 1;

-- Insert sub-categories for Network
INSERT IGNORE INTO cause_categories (name, name_en, parent_id, level) 
SELECT 'ISP Issue', 'ISP Issue', id, 2 FROM cause_categories WHERE name = 'Network' LIMIT 1;

INSERT IGNORE INTO cause_categories (name, name_en, parent_id, level) 
SELECT 'Router/Switch Failure', 'Router/Switch Failure', id, 2 FROM cause_categories WHERE name = 'Network' LIMIT 1;

INSERT IGNORE INTO cause_categories (name, name_en, parent_id, level) 
SELECT 'Cable Issue', 'Cable Issue', id, 2 FROM cause_categories WHERE name = 'Network' LIMIT 1;

-- Create view for easy access to ticket data with extended information
CREATE OR REPLACE VIEW ticket_full_view AS
SELECT 
    t.*,
    te.contact_person,
    te.location,
    te.effect,
    te.project_name,
    te.owner,
    te.noc_receiver,
    te.noc_closer,
    te.main_cause,
    te.sub_cause,
    te.group_cause1,
    te.group_cause2,
    te.cause_detail,
    te.cause_detail_eng,
    te.provider,
    te.province,
    te.downtime_hr,
    te.downtime_min,
    te.total_time,
    te.stop_clock,
    te.category,
    ti.login_temp,
    ti.sim_no,
    ti.sim_serial,
    ti.sim_operator,
    ti.sim_package,
    c.CusName as customer_name,
    c.CusAddress as customer_address,
    tm.name as team_name,
    u.username as created_by_username,
    ct.name as channel_name
FROM tickets t
LEFT JOIN ticket_extended te ON t.ticket_number = te.ticket_number
LEFT JOIN ticket_interim ti ON t.ticket_number = ti.ticket_number
LEFT JOIN KCS_DB.Customers c ON t.customer_number = c.CusCode
LEFT JOIN teams tm ON t.assigned_team = tm.id
LEFT JOIN users u ON t.username = u.username
LEFT JOIN channel_types ct ON t.channel_type = ct.id;

-- Create indexes for better performance on commonly searched fields
CREATE INDEX IF NOT EXISTS idx_tickets_customer_created ON tickets(customer_number, created_at);
CREATE INDEX IF NOT EXISTS idx_tickets_status_priority ON tickets(status, priority);
CREATE INDEX IF NOT EXISTS idx_tickets_team_status ON tickets(assigned_team, status);

-- Add trigger to automatically set closed_at when status changes to Closed
DELIMITER //
CREATE TRIGGER IF NOT EXISTS update_ticket_closed_at 
BEFORE UPDATE ON tickets
FOR EACH ROW 
BEGIN
    IF NEW.status = 'Closed' AND OLD.status != 'Closed' THEN
        SET NEW.closed_at = CURRENT_TIMESTAMP;
    ELSEIF NEW.status != 'Closed' AND OLD.status = 'Closed' THEN
        SET NEW.closed_at = NULL;
    END IF;
END//
DELIMITER ;

-- Create stored procedure for importing ticket data
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS ImportTicketData(
    IN p_ticket_number VARCHAR(20),
    IN p_customer_number VARCHAR(50),
    IN p_issue_details TEXT,
    IN p_priority VARCHAR(10),
    IN p_status VARCHAR(20),
    IN p_created_by VARCHAR(50),
    IN p_import_source VARCHAR(50)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- Insert main ticket
    INSERT INTO tickets (
        ticket_number, customer_number, issue_details, priority, 
        status, username, import_source, created_at
    ) VALUES (
        p_ticket_number, p_customer_number, p_issue_details, p_priority,
        p_status, p_created_by, p_import_source, NOW()
    );
    
    -- Set closed_at if status is Closed
    IF p_status = 'Closed' THEN
        UPDATE tickets SET closed_at = NOW() WHERE ticket_number = p_ticket_number;
    END IF;
    
    COMMIT;
END//
DELIMITER ;

-- Grant necessary permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE ON ticket_management.* TO 'your_app_user'@'localhost';

-- Create function to generate import batch ID
DELIMITER //
CREATE FUNCTION IF NOT EXISTS GenerateImportBatchId() 
RETURNS VARCHAR(50)
READS SQL DATA
DETERMINISTIC
BEGIN
    RETURN CONCAT('IMP_', DATE_FORMAT(NOW(), '%Y%m%d_%H%i%s'), '_', CONNECTION_ID());
END//
DELIMITER ;

COMMIT;


 SELECT 
    t.*,
    (SELECT SUM(TIMESTAMPDIFF(SECOND, 
        tsh1.changed_at, 
        COALESCE(tsh2.changed_at, CURRENT_TIMESTAMP)))
     FROM ticket_status_history tsh1
     LEFT JOIN ticket_status_history tsh2 
        ON tsh1.ticket_number = tsh2.ticket_number
        AND tsh2.id = (
            SELECT MIN(id) 
            FROM ticket_status_history 
            WHERE ticket_number = tsh1.ticket_number 
            AND id > tsh1.id
        )
     WHERE tsh1.ticket_number = t.ticket_number
     AND tsh1.status = 'Pending'
    ) as pending_seconds
FROM 
    tickets t
WHERE
	t.ticket_number= '25-18107' ;
	komsan.w
	
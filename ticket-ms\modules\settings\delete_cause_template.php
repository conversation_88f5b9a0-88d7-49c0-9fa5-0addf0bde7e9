<?php
header('Content-Type: application/json');

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

$response = ['success' => false, 'message' => ''];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['id'])) {
    $id = intval($_POST['id']);
    if ($id > 0) {
        try {
            // Check if template is being used in tickets table
            $check_stmt = $pdo->prepare("SELECT COUNT(*) FROM tickets WHERE cause_detail_template_id = :id");
            $check_stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $check_stmt->execute();
            $usage_count = $check_stmt->fetchColumn();

            if ($usage_count > 0) {
                $response['message'] = 'Cannot delete: This template is currently being used by ' . $usage_count . ' ticket(s).';
            } else {
                $stmt = $pdo->prepare("DELETE FROM ticket_cause_template WHERE id = :id");
                $stmt->bindParam(':id', $id, PDO::PARAM_INT);
                if ($stmt->execute()) {
                    $response['success'] = true;
                } else {
                    $response['message'] = 'Delete failed.';
                }
            }
        } catch (Exception $e) {
            $response['message'] = 'Error: ' . $e->getMessage();
        }
    } else {
        $response['message'] = 'Invalid ID.';
    }
} else {
    $response['message'] = 'Invalid request.';
}

echo json_encode($response);

-- --------------------------------------------------------
-- Host:                         ************
-- Server version:               10.5.27-MariaDB - MariaDB Server
-- Server OS:                    Linux
-- HeidiSQL Version:             12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREI<PERSON><PERSON>_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

-- Dumping structure for table ticket_management.chart_data
CREATE TABLE IF NOT EXISTS `chart_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `desc` varchar(50) DEFAULT NULL,
  `count` int(11) NOT NULL DEFAULT 0,
  `update_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `name_desc` (`name`,`desc`)
) ENGINE=InnoDB AUTO_INCREMENT=88 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Dumping data for table ticket_management.chart_data: ~43 rows (approximately)
INSERT IGNORE INTO `chart_data` (`id`, `name`, `desc`, `count`, `update_at`) VALUES
	(44, 'ticket_products', '-', 900, '2025-07-21 07:19:38'),
	(45, 'ticket_products', '4G (NET)', 6, '2025-07-21 07:19:38'),
	(46, 'ticket_products', 'Access Point', 1, '2025-07-21 07:19:38'),
	(47, 'ticket_products', 'Colo-Location', 5, '2025-07-21 07:19:38'),
	(48, 'ticket_products', 'Firewall', 718, '2025-07-21 07:19:38'),
	(49, 'ticket_products', 'GATEWAY-INTERCONNECT', 1, '2025-07-21 07:19:38'),
	(50, 'ticket_products', 'INTER GATEWAY', 8, '2025-07-21 07:19:38'),
	(51, 'ticket_products', 'Internet (FTTx)', 978, '2025-07-21 07:19:38'),
	(52, 'ticket_products', 'Internet (FTTx-BACKUP)', 20, '2025-07-21 07:19:38'),
	(53, 'ticket_products', 'Internet (FTTx-Bridge)', 89, '2025-07-21 07:19:38'),
	(54, 'ticket_products', 'Internet (FTTx-MAIN)', 2, '2025-07-21 07:19:38'),
	(55, 'ticket_products', 'Internet (MPLS)', 555, '2025-07-21 07:19:38'),
	(56, 'ticket_products', 'Internet (MPLS-BACKUP)', 26, '2025-07-21 07:19:38'),
	(57, 'ticket_products', 'Internet (MPLS-MAIN)', 148, '2025-07-21 07:19:38'),
	(58, 'ticket_products', 'Internet (WiFi+AP)', 2, '2025-07-21 07:19:38'),
	(59, 'ticket_products', 'Internet (xDSL)', 93, '2025-07-21 07:19:38'),
	(60, 'ticket_products', 'Internet+Voip', 33, '2025-07-21 07:19:38'),
	(61, 'ticket_products', 'iPSec', 898, '2025-07-21 07:19:38'),
	(62, 'ticket_products', 'iPSec (Backup)', 18, '2025-07-21 07:19:38'),
	(63, 'ticket_products', 'iPSec (Main)', 1726, '2025-07-21 07:19:38'),
	(64, 'ticket_products', 'iPSec (VPN)', 8, '2025-07-21 07:19:38'),
	(65, 'ticket_products', 'L2TP', 3909, '2025-07-21 07:19:38'),
	(66, 'ticket_products', 'L2TP (Backup LL)', 1, '2025-07-21 07:19:38'),
	(67, 'ticket_products', 'L2TP (Backup)', 3015, '2025-07-21 07:19:38'),
	(68, 'ticket_products', 'L2TP (Backup) + WiFi', 518, '2025-07-21 07:19:38'),
	(69, 'ticket_products', 'L2TP (Main)', 2914, '2025-07-21 07:19:38'),
	(70, 'ticket_products', 'L2TP (Main) + WiFi', 4, '2025-07-21 07:19:38'),
	(71, 'ticket_products', 'L2TP Over NET (Route)', 422, '2025-07-21 07:19:38'),
	(72, 'ticket_products', 'L2TP+Net', 17, '2025-07-21 07:19:38'),
	(73, 'ticket_products', 'L2TP+Net (Cisco 877)', 2, '2025-07-21 07:19:38'),
	(74, 'ticket_products', 'LED', 1, '2025-07-21 07:19:38'),
	(75, 'ticket_products', 'MPLS', 380, '2025-07-21 07:19:38'),
	(76, 'ticket_products', 'MPLS (Backup)', 12, '2025-07-21 07:19:38'),
	(77, 'ticket_products', 'MPLS (Main)', 354, '2025-07-21 07:19:38'),
	(78, 'ticket_products', 'MPLS (Private Link)', 3, '2025-07-21 07:19:38'),
	(79, 'ticket_products', 'Server', 3, '2025-07-21 07:19:38'),
	(80, 'ticket_products', 'VOIP', 22, '2025-07-21 07:19:38'),
	(81, 'ticket_products', 'VPN', 185, '2025-07-21 07:19:38'),
	(82, 'ticket_products', 'VPN (Main)', 5, '2025-07-21 07:19:38'),
	(83, 'ticket_products', 'VPN+WiFi', 17, '2025-07-21 07:19:38'),
	(84, 'ticket_products', 'Zoom Online', 2, '2025-07-21 07:19:38'),
	(85, 'ticket_products', 'Zoom Phone', 163, '2025-07-21 07:19:38'),
	(86, 'ticket_products', 'Zound Pod Room', 8, '2025-07-21 07:19:38');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;

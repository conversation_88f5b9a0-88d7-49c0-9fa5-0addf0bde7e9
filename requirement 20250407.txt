
=== Ticket System Requirement 2025-07-08

1. การ Assign งานให้ทำระดับ user หรือตัวบุคคล

2. กำหนดค่า Default การสร้าง ticket จากระบบ cs monitor
	-- Ticket Type = Incicent
	-- Symptoms = link down(disconected)
	-- Severity = S4-Low Impact
	-- Priority = Medium
	-- Channel Type = Monitor System
	-- ส่วนที่เหลือมาจากการเลือก ตัวเลือกในขั้นตอนสร้าง
3. ข้อมูล Product Type ให้ดึงมาแสดงจากระบบ csdb

4. เพิ่ม Field สำหรับใช้คำนวณเวลา เริ่มต้น และเวลาปิด Ticket
	-- Ticket Start(Datetime)
	--- กรณีสร้างจากระบบ monitor ให้ใช้เวลา monitor downtime
	--- กรณีสร้างจากระบบ หน้า ticket โดยตรง ผู้ใช้ต้องกำหนดเอง
	
	-- Ticket Closed(Datetime)
	--- กรณีสร้างจากระบบ monitor ให้ใช้เวลา monitor uptime
	--- กรณีสร้างจากระบบ หน้า ticket โดยตรง ผู้ใช้ต้องกำหนดเอง
	
5. เพิ่ม Sub status
	-- หากยังไม่มีการดำเนินการใดๆ ระบบจะเปลี่ยน status เป็น Overdue หลังจาก 2Hr. ของการ update comment ล่าสุด
	-- Open
	-- In Progress
	--- Wait Customer support
	--- Wait Implement
	--- Wait PM
	--- Wait Operator
	-- Pending(ไม่คำนวณเวลา)
	--- Wait Customer
	-- Closed
	
6. การ post comments (รายละเอียดการดำเนินการแก้ไข)
	-- เพิ่มตัวเลือก การดำเนินการ
	--- ประสานงานลูกค้า(ปลายทาง)
	--- ประสานงานลูกค้า(ส่วนกลาง)
	--- ประสานงาน หน่วยงานภายใน
	--- ประสานงาน Operator
	--- ประสานงานช่างเพื่อเข้าดำเนินการ
	
7. เพิ่ม root cause ต่อจาก Issue Details
	-- โดยให้สามารถเลือกได้จาก template
	
8. การค้นหา Search bar
	-- เพิ่มการค้นหาจาก Login ได้ด้วย

9. หน้ารายการ Ticket lists
	-- เพิ่ม Field > CS No.(Ref)
	-- เพิ่ม Field > Login
	-- ไม่ต้องแสดง Ticket ที่ Closed แล้ว


Ticket Type: Incident(default)
assign : person
Product Type: ดึงมาจาก csdb

start down: from monitoring (เพิ่ม field)
uptime : from monitoring (เพิ่ม field)

status overdue: >= 2 hr. จากการ update comment ล่าสุด

เพิ่ม template ตอนปิดงาน ใส่ comment

จากระบบ monitor ใส่ค่า default ตามลักษณะ product

for comment:
	- ประเภทการประสานงาน ในแต่ละ comment
		-- ประสานงานลูกค้า
		-- ประสานงาน operater
		-- xxx
search ticket 
 ++login

หน้า list
	default : ยกเว้ย closed
	cs no(Ref),login : หน้า list

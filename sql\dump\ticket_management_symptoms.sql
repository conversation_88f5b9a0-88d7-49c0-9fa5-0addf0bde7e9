-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: ************    Database: ticket_management
-- ------------------------------------------------------
-- Server version	5.5.5-10.5.27-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `symptoms`
--

DROP TABLE IF EXISTS `symptoms`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `symptoms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(20) NOT NULL,
  `name` varchar(100) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `symptoms`
--

LOCK TABLES `symptoms` WRITE;
/*!40000 ALTER TABLE `symptoms` DISABLE KEYS */;
INSERT INTO `symptoms` VALUES (21,'LINK_DOWN','link down (disconnected)',1,'2025-04-09 10:26:05'),(22,'LINK_UPDOWN','link up/down',1,'2025-04-09 10:26:05'),(23,'FUNC_ERROR','can not use some function/application',1,'2025-04-09 10:26:05'),(24,'VOIP_ERROR','VoIP ใช้งานไม่ได้',1,'2025-04-09 10:26:05'),(25,'EQUIP_ERROR','Product Equipment มีปัญหา',1,'2025-04-09 10:26:05'),(26,'WF_CLI','WF-CLI',1,'2025-04-09 10:26:05'),(27,'WF_CLI_IP','WF-CLI-IP',1,'2025-04-09 10:26:05'),(28,'BF','BF',1,'2025-04-09 10:26:05'),(29,'BF_CLI','BF-CLI',1,'2025-04-09 10:26:05'),(30,'BF_VOIP','BF-VoIP',1,'2025-04-09 10:26:05'),(31,'E1','E1',1,'2025-04-09 10:26:05'),(32,'E1_CLI','E1-CLI',1,'2025-04-09 10:26:05'),(33,'E1_VOIP','E1-VoIP',1,'2025-04-09 10:26:05'),(34,'SIP_CLI','SIP-CLI',1,'2025-04-09 10:26:05'),(35,'SIP_VOIP','SIP-VoIP',1,'2025-04-09 10:26:05'),(36,'CLOUD_FONO','Cloud Phone Fono (C1)',1,'2025-04-09 10:26:05'),(37,'CLOUD_DELTA','Cloud Phone Deltapath (FrSIP)',1,'2025-04-09 10:26:05'),(38,'ZOOM_MEET','Zoom Meeting',1,'2025-04-09 10:26:05'),(39,'ZOOM_PHONE','Zoom Phone',1,'2025-04-09 10:26:05'),(40,'OTHER','อื่นๆ',1,'2025-04-09 10:26:05');
/*!40000 ALTER TABLE `symptoms` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-18 16:44:59

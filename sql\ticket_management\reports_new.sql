SELECT `pj`.`Project_Name` AS `project_name`,
       `pt`.`Connection_Type` AS `producttype`,
       `m`.<PERSON>gin AS `login_name`,
       `c`.`CusName` AS `customer_name`,
       `m`.Site_Address AS `site_address`,
       tk.*
FROM ( SELECT `t`.`ticket_number` AS `ticket_number`,
      `t`.`customer_number` AS `customer_number`,
      `t`.`csno` AS `csno`,
      `t`.`issue_details` AS `issue_details`,
      `t`.`priority` AS `priority`,
      `t`.`status` AS `status`,
      `t`.`assigned_worker` AS assigned_staff,
      `t`.`created_at` AS `created_at`,
      `t`.`updated_at` AS `updated_at`,
      `t`.`open_at` AS `open_at`,
      `t`.`closed_at` AS `closed_at`,
      `t`.`estimated_stop_end` AS `estimated_stop_clock_end`,
      `t`.sub_status AS `status_detail`,
      `t`.`updated_by` AS `updated_by`,
      `tt`.`name` AS `ticket_type`,
      `af`.`name` AS `affecting_service`,
      `t`.`symptoms_details` AS `symptoms_details`,
      `t`.`product_type` AS `product_type`,
      `sv`.`name` AS `severity`,
      `ch`.`name` AS `channel_type`,
      `ti`.`login_temp` AS `login_temp`,
      `ti`.`sim_no` AS `sim_no`,
      `ti`.`sim_serial` AS `sim_serial`,
      `ti`.`sim_operator` AS `sim_operator`,
      `ti`.`sim_package` AS `sim_package`,
      `ct`.`name` AS `channel_name`,
      `tp`.main_cause,
      `tp`.sub_cause,
      `tp`.issue_cause,
      `tp`.group_cause,
      `tp`.cause_name,
      `tp`.cause_name_en,
      `u`.`username` AS `created_by`,
      (SELECT SUM(TIMESTAMPDIFF(SECOND, tsh1.changed_at, COALESCE(tsh2.changed_at, CURRENT_TIMESTAMP)))
      FROM ticket_status_history tsh1
      LEFT JOIN ticket_status_history tsh2 ON tsh1.ticket_number = tsh2.ticket_number
      AND tsh2.id =
        (SELECT MIN(id)
         FROM ticket_status_history
         WHERE ticket_number = tsh1.ticket_number
           AND id > tsh1.id )
      WHERE tsh1.ticket_number = t.ticket_number
        AND tsh1.status = 'Pending' ) AS pending_seconds
      FROM `ticket_management`.`tickets` `t`
      LEFT JOIN `ticket_management`.`ticket_extended` `te` ON(`t`.`ticket_number` = `te`.`ticket_number`)
      LEFT JOIN `ticket_management`.`ticket_interim` `ti` ON(`t`.`ticket_number` = `ti`.`ticket_number`)
      LEFT JOIN `ticket_management`.`users` `u` ON(`t`.`username` = `u`.`username`)
      LEFT JOIN `ticket_management`.`channel_types` `ct` ON(`t`.`channel_type` = `ct`.`id`)
      LEFT JOIN `ticket_management`.`ticket_cause_template` `tp` ON(`t`.`cause_detail_template_id` = `tp`.`id`)
      LEFT JOIN `ticket_management`.`ticket_type` `tt` ON(`t`.`ticket_type` = `tt`.`id`)
      LEFT JOIN `ticket_management`.`affecting_service_types` `af` ON(`t`.`affecting_service` = `af`.`id`)
      LEFT JOIN `ticket_management`.`severity_types` `sv` ON(`t`.`severity` = `sv`.`code`)
      LEFT JOIN `ticket_management`.`channel_types` `ch` ON(`t`.`channel_type` = `ch`.`id`)
      
      LIMIT 1000) AS tk
LEFT JOIN `KCS_DB`.`Main` `m` ON(`tk`.`csno` = `m`.Ref)
LEFT JOIN `KCS_DB`.`Connection_Type` `pt` ON(`m`.`Connection_Type` = `pt`.`Connection_ID`)
LEFT JOIN `KCS_DB`.`Customers` `c` ON(`tk`.`customer_number` = `c`.`CusCode`)
LEFT JOIN `KCS_DB`.`Project` `pj` ON (`m`.`Project_ID` = `pj`.`Project_ID`)


<?php
session_start();
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);

require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Fetch user settings and user data
$user_id = $_SESSION['user_id'];

// Check if user is a member of System Admin team (id=3)
$is_system_admin = false;
$admin_check_query = "SELECT COUNT(*) FROM users WHERE id = :user_id AND `role` = 'admin'";
//"SELECT COUNT(*) FROM team_members WHERE user_id = :user_id AND team_id = 3";
$admin_check_stmt = $pdo->prepare($admin_check_query);
$admin_check_stmt->bindParam(':user_id', $user_id);
$admin_check_stmt->execute();
$is_system_admin = ($admin_check_stmt->fetchColumn() > 0);
// Check if user has admin privileges
if (!$is_system_admin) {
    header("Location: ../../index.php?error=Access denied");
    exit();
}

// Pagination
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// Get import history
try {
    // Count total records
    $countQuery = "SELECT COUNT(*) FROM import_history";
    $totalRecords = $pdo->query($countQuery)->fetchColumn();
    $totalPages = ceil($totalRecords / $limit);
    
    // Get import history with user information
    $query = "SELECT 
        ih.*,
        u.username as imported_by_username,
        TIMESTAMPDIFF(SECOND, ih.started_at, ih.completed_at) as duration_seconds
    FROM import_history ih
    LEFT JOIN users u ON ih.imported_by = u.id
    ORDER BY ih.started_at DESC
    LIMIT $limit OFFSET $offset";
    
    //var_dump($query);

    $stmt = $pdo->prepare($query);
    $stmt->execute();
    $imports = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $imports = [];
    $totalPages = 0;
    $error = "Error loading import history: " . $e->getMessage();
}

// Handle delete action
if ($_POST['action'] ?? '' === 'delete' && !empty($_POST['import_id'])) {
    try {
        $deleteQuery = "DELETE FROM import_history WHERE id = ?";
        $deleteStmt = $pdo->prepare($deleteQuery);
        $deleteStmt->execute([$_POST['import_id']]);
        
        header("Location: import_history.php?deleted=1");
        exit();
    } catch (Exception $e) {
        $error = "Error deleting import record: " . $e->getMessage();
    }
}

require_once '../../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0"><i class="fas fa-history"></i> Import History</h4>
                    <div>
                        <a href="import.php" class="btn btn-outline-light">
                            <i class="fas fa-upload"></i> New Import
                        </a>
                        <a href="list.php" class="btn btn-outline-light ml-2">
                            <i class="fas fa-list"></i> Tickets
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($_GET['deleted'])): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> Import record deleted successfully.
                        </div>
                    <?php endif; ?>

                    <?php if (empty($imports)): ?>
                        <div class="alert alert-info text-center">
                            <i class="fas fa-info-circle fa-3x mb-3"></i>
                            <h5>No Import History Found</h5>
                            <p>No ticket imports have been performed yet.</p>
                            <a href="import.php" class="btn btn-primary">
                                <i class="fas fa-upload"></i> Start Your First Import
                            </a>
                        </div>
                    <?php else: ?>
                        <!-- Summary Cards -->
                        <div class="row mb-4">
                            <?php
                            $totalImports = count($imports);
                            $successfulImports = array_filter($imports, function($imp) { return $imp['status'] === 'completed'; });
                            $failedImports = array_filter($imports, function($imp) { return $imp['status'] === 'failed'; });
                            $totalTicketsImported = array_sum(array_column($imports, 'success_count'));
                            ?>
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h6>Total Imports</h6>
                                                <h3><?php echo $totalRecords; ?></h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-upload fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h6>Successful</h6>
                                                <h3><?php echo count($successfulImports); ?></h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-check-circle fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h6>Failed</h6>
                                                <h3><?php echo count($failedImports); ?></h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-times-circle fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h6>Tickets Imported</h6>
                                                <h3><?php echo number_format($totalTicketsImported); ?></h3>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-ticket-alt fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Import History Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Filename</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <th>Total Rows</th>
                                        <th>Success</th>
                                        <th>Errors</th>
                                        <th>Imported By</th>
                                        <th>Started</th>
                                        <th>Duration</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($imports as $import): ?>
                                        <tr>
                                            <td><?php echo $import['id']; ?></td>
                                            <td>
                                                <span class="text-truncate d-inline-block" style="max-width: 200px;" 
                                                      title="<?php echo htmlspecialchars($import['original_filename']); ?>">
                                                    <?php echo htmlspecialchars($import['original_filename']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge badge-secondary">
                                                    <?php echo ucfirst($import['import_type']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $statusClass = [
                                                    'completed' => 'success',
                                                    'failed' => 'danger',
                                                    'processing' => 'warning',
                                                    'pending' => 'info'
                                                ][$import['status']] ?? 'secondary';
                                                ?>
                                                <span class="badge badge-<?php echo $statusClass; ?>">
                                                    <?php echo ucfirst($import['status']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo number_format($import['total_rows']); ?></td>
                                            <td>
                                                <span class="text-success font-weight-bold">
                                                    <?php echo number_format($import['success_count']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php if ($import['error_count'] > 0): ?>
                                                    <span class="text-danger font-weight-bold">
                                                        <?php echo number_format($import['error_count']); ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-muted">0</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo htmlspecialchars($import['imported_by_username']); ?></td>
                                            <td>
                                                <small>
                                                    <?php echo date('Y-m-d H:i:s', strtotime($import['started_at'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <?php if ($import['duration_seconds']): ?>
                                                    <small>
                                                        <?php 
                                                        $duration = $import['duration_seconds'];
                                                        if ($duration < 60) {
                                                            echo $duration . 's';
                                                        } else {
                                                            echo floor($duration / 60) . 'm ' . ($duration % 60) . 's';
                                                        }
                                                        ?>
                                                    </small>
                                                <?php else: ?>
                                                    <small class="text-muted">-</small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <?php if ($import['error_count'] > 0): ?>
                                                        <button class="btn btn-outline-warning btn-sm" 
                                                                onclick="viewErrors(<?php echo $import['id']; ?>)"
                                                                title="View Errors">
                                                            <i class="fas fa-exclamation-triangle"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    
                                                    <button class="btn btn-outline-danger btn-sm" 
                                                            onclick="deleteImport(<?php echo $import['id']; ?>)"
                                                            title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                            <nav aria-label="Import history pagination">
                                <ul class="pagination justify-content-center">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?>">Previous</a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                        <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $totalPages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?>">Next</a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Error Details Modal -->
<div class="modal fade" id="errorModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Errors</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="errorContent">Loading...</div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Form -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="import_id" id="deleteImportId">
</form>

<script>
function viewErrors(importId) {
    $('#errorModal').modal('show');
    $('#errorContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Loading errors...</div>');
    
    fetch('import_errors.php?import_id=' + importId)
        .then(response => response.text())
        .then(data => {
            $('#errorContent').html(data);
        })
        .catch(error => {
            $('#errorContent').html('<div class="alert alert-danger">Error loading error details: ' + error.message + '</div>');
        });
}

function deleteImport(importId) {
    if (confirm('Are you sure you want to delete this import record? This action cannot be undone.')) {
        document.getElementById('deleteImportId').value = importId;
        document.getElementById('deleteForm').submit();
    }
}
</script>

<?php
require_once '../../includes/main_script_loader.php';
require_once '../../includes/footer.php';
?>

WITH 	LatestEvent AS (
		SELECT e.event_id,d.device_id,d.hostname,	MAX(e.datetime) AS max_datetime	
		FROM
			devices d INNER JOIN eventlog e ON d.device_id = e.device_id
		WHERE 
		d.status = 0 AND e.message LIKE '%Down from icmp%'
		GROUP BY 	d.hostname
	)
	
SELECT
	e.event_id,	d.device_id,d.hostname,	d.sysName,d.display,
	d.status,d.purpose,d.hardware,d.Status_ID,e.datetime,
	e.message,dg.desc,d.Mon_timeid,d.Mon_timestart,	d.Mon_timeend,	'' AS `Owner`
FROM
	devices d INNER JOIN eventlog e ON d.device_id = e.device_id
	INNER JOIN device_groups dg ON d.purpose = dg.name
	INNER JOIN LatestEvent le ON d.hostname = le.hostname	AND e.datetime = le.max_datetime
WHERE
	e.message LIKE '%Down from icmp%'
	AND dg.desc IN ('D1', 'D2', 'D3', 'D4', 'D5', 'D6', 'D7'	)
	AND le.max_datetime >= DATE_SUB(NOW(), INTERVAL 3 DAY)
	AND d.Mon_Day IN (1, 2)
	AND d.Status_ID IN (3, 44)
	AND d.display NOT IN ('12ACTD2400297')
	AND (	(:currentTime BETWEEN d.Mon_timestart AND d.Mon_timeend)	
	OR (d.Mon_timestart > d.Mon_timeend
			AND (	:currentTime BETWEEN d.Mon_timestart AND '23:59:59'
				OR :currentTime BETWEEN '00:00:00'	AND d.Mon_timeend	)
		)
	)
ORDER BY
	e.datetime DESC
<?php
session_start();
/*
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
*/
require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';


// Fetch user settings and user data
$user_id = $_SESSION['user_id'];

// Check if user is a member of System Admin team (id=3)
$is_system_admin = false;
$admin_check_query = "SELECT COUNT(*) FROM users WHERE id = :user_id AND `role` = 'admin'";
//"SELECT COUNT(*) FROM team_members WHERE user_id = :user_id AND team_id = 3";
$admin_check_stmt = $pdo->prepare($admin_check_query);
$admin_check_stmt->bindParam(':user_id', $user_id);
$admin_check_stmt->execute();
$is_system_admin = ($admin_check_stmt->fetchColumn() > 0);
// Check if user has admin privileges
if (!$is_system_admin) {
    header("Location: ../../index.php?error=Access denied");
    exit();
}

$errors = [];
$success_message = '';

// Define the expected Excel columns
$excel_columns = [
    'No.',
    'Customer Ticket',
    'Contact Person',
    'Login',
    'Contact Channel',
    'Project Name',
    'Owner',
    'NOC (ผู้รับแจ้ง)',
    'CS No.',
    'Location',
    'Effect',
    'Ticket Open',
    'Ticket Close',
    'Downtime (hr)',
    'เวลารวมทั้งหมด',
    'Stop Clock',
    'Downtime (min)',
    'Category',
    'Main Cause',
    'Sub Cause',
    'Group cause1',
    'Group cause2',
    'Cause Detail',
    'Cause_Detail_Eng',
    'NOC (ผู้ปิดงาน)',
    'Provider',
    'Province',
    'Login Temp',
    'Sim No.',
    'Sim S/N',
    'Sim Operator'
];
//'Customer No.'
// Define database field mappings
$db_fields = [
    'ticket_number' => 'Ticket Number (Auto-generated)',
    'customer_number' => 'Customer Number/Code',
    'issue_details' => 'Issue Details/Description',
    'priority' => 'Priority (High/Medium/Low)',
    'status' => 'Status (Open/In Progress/Pending/Closed)',
    'assigned_team' => 'Assigned Team ID',
    'username' => 'Created By Username',
    'ticket_type' => 'Ticket Type',
    'affecting_service' => 'Affecting Service',
    'symptoms_details' => 'Symptoms Details',
    'product_type' => 'Product Type',
    'severity' => 'Severity',
    'channel_type' => 'Channel Type ID',
    'csno' => 'CS Number',
    'contact_person' => 'Contact Person',
    'location' => 'Location',
    'effect' => 'Effect/Impact',
    'project_name' => 'Project Name',
    'owner' => 'Owner',
    'noc_receiver' => 'NOC (ผู้รับแจ้ง)',
    'noc_closer' => 'NOC (ผู้ปิดงาน)',
    'main_cause' => 'Main Cause',
    'sub_cause' => 'Sub Cause',
    'group_cause1' => 'Group Cause 1',
    'group_cause2' => 'Group Cause 2',
    'cause_detail' => 'Cause Detail',
    'cause_detail_eng' => 'Cause Detail (English)',
    'provider' => 'Provider',
    'province' => 'Province',
    'downtime_hr' => 'Downtime (Hours)',
    'downtime_min' => 'Downtime (Minutes)',
    'total_time' => 'Total Time',
    'stop_clock' => 'Stop Clock',
    'category' => 'Category',
    'login_temp' => 'Login Temp',
    'sim_no' => 'SIM Number',
    'sim_serial' => 'SIM Serial Number',
    'sim_operator' => 'SIM Operator',
    'ticket_open' => 'Ticket Open Date',
    'ticket_close' => 'Ticket Close Date'
];

require_once '../../includes/header.php';
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h4 class="mb-0"><i class="fas fa-file-excel"></i> Import Old Tickets from Excel</h4>
                    <a href="list.php" class="btn btn-outline-light">
                        <i class="fas fa-arrow-left"></i> Back to Tickets
                    </a>
                </div>
                <div class="card-body">
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo htmlspecialchars($error); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success">
                            <?php echo htmlspecialchars($success_message); ?>
                        </div>
                    <?php endif; ?>

                    <!-- Step 1: File Upload -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-upload"></i> Step 1: Upload Excel File</h5>
                                </div>
                                <div class="card-body">
                                    <form id="uploadForm" enctype="multipart/form-data">
                                        <div class="form-group">
                                            <label for="excel_file">Select Excel File (.xlsx, .xls)</label>
                                            <input type="file" class="form-control-file" id="excel_file" name="excel_file"
                                                accept=".xlsx,.xls" required>
                                            <small class="form-text text-muted">
                                                Maximum file size: 10MB. Supported formats: .xlsx, .xls
                                            </small>
                                        </div>
                                        <button type="button" class="btn btn-primary" onclick="uploadFile()">
                                            <i class="fas fa-upload"></i> Upload & Preview
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5><i class="fas fa-info-circle"></i> Expected Excel Columns</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <?php
                                        $half = ceil(count($excel_columns) / 2);
                                        $first_half = array_slice($excel_columns, 0, $half);
                                        $second_half = array_slice($excel_columns, $half);
                                        ?>
                                        <div class="col-6">
                                            <ul class="list-unstyled small">
                                                <?php foreach ($first_half as $column): ?>
                                                    <li><i class="fas fa-check text-success"></i> <?php echo htmlspecialchars($column); ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                        <div class="col-6">
                                            <ul class="list-unstyled small">
                                                <?php foreach ($second_half as $column): ?>
                                                    <li><i class="fas fa-check text-success"></i> <?php echo htmlspecialchars($column); ?></li>
                                                <?php endforeach; ?>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Column Mapping (Hidden initially) -->
                    <div id="mappingSection" class="mt-4" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-exchange-alt"></i> Step 2: Map Excel Columns to Database Fields</h5>
                            </div>
                            <div class="card-body">
                                <form id="mappingForm">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6>Excel Columns Found:</h6>
                                            <div id="excelColumns" class="border p-3 mb-3" style="max-height: 400px; overflow-y: auto;">
                                                <!-- Excel columns will be populated here -->
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <h6>Database Field Mappings:</h6>
                                            <div id="fieldMappings" style="max-height: 400px; overflow-y: auto;">
                                                <!-- Field mappings will be populated here -->
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3">
                                        <h6>Import Options:</h6>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="skipFirstRow" checked>
                                            <label class="form-check-label" for="skipFirstRow">
                                                Skip first row (header row)
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="validateData" checked>
                                            <label class="form-check-label" for="validateData">
                                                Validate data before import
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="createMissingCustomers">
                                            <label class="form-check-label" for="createMissingCustomers">
                                                Create missing customers automatically
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mt-4">
                                        <button type="button" class="btn btn-success" onclick="startImport()">
                                            <i class="fas fa-database"></i> Start Import
                                        </button>
                                        <button type="button" class="btn btn-secondary ml-2" onclick="resetForm()">
                                            <i class="fas fa-redo"></i> Reset
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Import Progress (Hidden initially) -->
                    <div id="progressSection" class="mt-4" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-spinner fa-spin"></i> Step 3: Import Progress</h5>
                            </div>
                            <div class="card-body">
                                <div class="progress mb-3">
                                    <div id="importProgress" class="progress-bar" role="progressbar" style="width: 0%">0%</div>
                                </div>
                                <div id="importStatus">Preparing import...</div>
                                <div id="importResults" class="mt-3"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    let uploadedFile = null;
    let excelData = null;

    function uploadFile() {
        const fileInput = document.getElementById('excel_file');
        const file = fileInput.files[0];

        if (!file) {
            alert('Please select a file first.');
            return;
        }

        if (!file.name.match(/\.(xlsx|xls)$/)) {
            alert('Please select a valid Excel file (.xlsx or .xls).');
            return;
        }

        if (file.size > 10 * 1024 * 1024) { // 10MB limit
            alert('File size must be less than 10MB.');
            return;
        }

        const formData = new FormData();
        formData.append('excel_file', file);
        formData.append('action', 'upload');

        // Show loading
        const uploadBtn = document.querySelector('button[onclick="uploadFile()"]');
        const originalText = uploadBtn.innerHTML;
        uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
        uploadBtn.disabled = true;

        fetch('import_process.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                uploadBtn.innerHTML = originalText;
                uploadBtn.disabled = false;

                if (data.success) {
                    uploadedFile = file.name;
                    excelData = data.data;
                    showMappingSection(data.columns);
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                uploadBtn.innerHTML = originalText;
                uploadBtn.disabled = false;
                alert('Error uploading file: ' + error.message);
            });
    }

    function showMappingSection(excelColumns) {
        document.getElementById('mappingSection').style.display = 'block';

        // Populate Excel columns
        const excelColumnsDiv = document.getElementById('excelColumns');
        excelColumnsDiv.innerHTML = '';
        excelColumns.forEach((column, index) => {
            const div = document.createElement('div');
            div.className = 'mb-2 p-2 border rounded';
            div.innerHTML = `<strong>Column ${index + 1}:</strong> ${column}`;
            excelColumnsDiv.appendChild(div);
        });

        // Populate field mappings
        const fieldMappingsDiv = document.getElementById('fieldMappings');
        fieldMappingsDiv.innerHTML = '';

        <?php foreach ($db_fields as $field => $description): ?>
            const mapping<?php echo ucfirst($field); ?> = document.createElement('div');
            mapping<?php echo ucfirst($field); ?>.className = 'form-group mb-2';
            mapping<?php echo ucfirst($field); ?>.innerHTML = `
        <label class="small font-weight-bold"><?php echo htmlspecialchars($description); ?></label>
        <select class="form-control form-control-sm" name="mapping[<?php echo $field; ?>]">
            <option value="">-- Skip this field --</option>
            ${excelColumns.map((col, idx) => `<option value="${idx}">${col}</option>`).join('')}
        </select>
    `;
            fieldMappingsDiv.appendChild(mapping<?php echo ucfirst($field); ?>);
        <?php endforeach; ?>

        // Auto-map some obvious columns
        autoMapColumns(excelColumns);
    }

    function autoMapColumns(excelColumns) {
        const autoMappings = {
            'ticket_number': ['Customer Ticket'],
            'customer_number': ['Customer No.', 'CusCode', 'customer code'],
            'contact_person': ['contact person', 'contact'],
            'csno': ['cs no', 'cs number', 'cs no.'],
            'location': ['location'],
            'project_name': ['project name', 'project'],
            'owner': ['owner'],
            'noc_receiver': ['noc (ผู้รับแจ้ง)', 'noc receiver'],
            'noc_closer': ['noc (ผู้ปิดงาน)', 'noc closer'],
            'main_cause': ['main cause'],
            'sub_cause': ['sub cause'],
            'group_cause1': ['Group cause1'],
            'group_cause2': ['Group cause2'],
            'cause_detail': ['Cause Detail'],
            'cause_detail_eng': ['Cause_Detail_Eng'],
            'total_time': ['Total Time', 'เวลารวมทั้งหมด'],
            'stop_clock': ['Stop Clock'],
            'category': ['Category'],
            'provider': ['provider'],
            'province': ['province'],
            'login_temp': ['login temp'],
            'sim_no': ['sim no', 'sim no.'],
            'sim_serial': ['sim s/n', 'sim serial'],
            'sim_operator': ['sim operator'],
            'ticket_open': ['ticket open'],
            'ticket_close': ['ticket close'],
            'downtime_hr': ['downtime (hr)', 'downtime hr'],
            'downtime_min': ['downtime (min)', 'downtime min'],
            'channel_type': ['contact channel', 'channel'],
            'issue_details': ['issue details', 'Cause Detail'],
            'product_type': ['Category'],
            'affecting_service': ['Effect'],
            'effect': ['Effect'],
            'username': ['noc (ผู้รับแจ้ง)']
        };

        Object.keys(autoMappings).forEach(field => {
            const select = document.querySelector(`select[name="mapping[${field}]"]`);
            if (select) {
                const matchingColumn = excelColumns.findIndex(col =>
                    autoMappings[field].some(pattern =>
                        col.toLowerCase().includes(pattern.toLowerCase())
                    )
                );
                if (matchingColumn !== -1) {
                    select.value = matchingColumn;
                }
            }
        });
    }

    function startImport() {
        const mappingForm = document.getElementById('mappingForm');
        const formData = new FormData(mappingForm);
        formData.append('action', 'import');
        formData.append('filename', uploadedFile);
        formData.append('skip_first_row', document.getElementById('skipFirstRow').checked ? '1' : '0');
        formData.append('validate_data', document.getElementById('validateData').checked ? '1' : '0');
        formData.append('create_missing_customers', document.getElementById('createMissingCustomers').checked ? '1' : '0');

        document.getElementById('progressSection').style.display = 'block';
        document.getElementById('importStatus').textContent = 'Starting import...';

        fetch('import_process.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log(data);

                if (data.success) {
                    updateProgress(100);
                    document.getElementById('importStatus').textContent = 'Import completed successfully!';
                    document.getElementById('importResults').innerHTML = `
                <div class="alert alert-success">
                    <h6>Import Summary:</h6>
                    <ul>
                        <li>Total rows processed: ${data.total_rows}</li>
                        <li>Successfully imported: ${data.success_count}</li>
                        <li>Errors: ${data.error_count}</li>
                    </ul>
                    ${data.errors && data.errors.length > 0 ?
                        '<h6>Errors:</h6><ul>' + data.errors.map(err => `<li>${err}</li>`).join('') + '</ul>'
                        : ''
                    }
                </div>
            `;
                } else {
                    document.getElementById('importStatus').textContent = 'Import failed: ' + data.message;
                    document.getElementById('importResults').innerHTML = `
                <div class="alert alert-danger">
                    Error: ${data.message}
                </div>
            `;
                }
            })
            .catch(error => {
                document.getElementById('importStatus').textContent = 'Import failed: ' + error.message;
                document.getElementById('importResults').innerHTML = `
            <div class="alert alert-danger">
                Error: ${error.message}
            </div>
        `;
            });
    }

    function updateProgress(percent) {
        const progressBar = document.getElementById('importProgress');
        progressBar.style.width = percent + '%';
        progressBar.textContent = percent + '%';
    }

    function resetForm() {
        document.getElementById('uploadForm').reset();
        document.getElementById('mappingSection').style.display = 'none';
        document.getElementById('progressSection').style.display = 'none';
        uploadedFile = null;
        excelData = null;
    }
</script>

<?php
require_once '../../includes/main_script_loader.php';
require_once '../../includes/footer.php';
?>
<?php
session_start();
require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';


// Fetch user settings and user data
$user_id = $_SESSION['user_id'];
$settings = getUserSettings($user_id);
$userdata = $_SESSION['userdata'];

// Check if user is an admin
$is_system_admin = IsAdminCheck($user_id);

// If user is admin, fetch all users and teams for the team assignment section
$all_users = [];
$all_teams = [];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'update_settings') {
    $success_message = '';
    $error_message = '';

    // Update user settings
    $new_settings = [
        'email_notifications' => isset($_POST['email_notifications']) ? 1 : 0,
        'sms_notifications' => isset($_POST['sms_notifications']) ? 1 : 0,
        'system_notifications' => isset($_POST['system_notifications']) ? 1 : 0,
        'theme' => $_POST['theme'],
        'language' => $_POST['language'],
        'items_per_page' => intval($_POST['items_per_page'])
    ];

    try {
        updateUserSettings($user_id, $new_settings);
        $settings = getUserSettings($user_id); // Refresh settings
        $success_message = "Settings updated successfully!";
        header("Location: index.php?message=" . urlencode($success_message));
        exit();
    } catch (Exception $e) {
        $error_message = "Error updating settings: " . $e->getMessage();
    }
}

if ($is_system_admin) {
    // Fetch all users
    $users_query = "SELECT * 
                        FROM users 
                        WHERE status = 'active' AND division='Operation'
                        ORDER BY fullname";
    $users_stmt = $pdo->query($users_query);
    $all_users = $users_stmt->fetchAll(PDO::FETCH_ASSOC);

    // Fetch all teams
    $teams_query = "SELECT id, name, description FROM teams ORDER BY name";
    $teams_stmt = $pdo->query($teams_query);
    $all_teams = $teams_stmt->fetchAll(PDO::FETCH_ASSOC);

    $divisions_query = "SELECT id, name FROM users_division ORDER BY name";
    $divisions_stmt = $pdo->query($divisions_query);
    $all_divisions = $divisions_stmt->fetchAll(PDO::FETCH_ASSOC);

    $departments_query = "SELECT id, name FROM users_department ORDER BY name";
    $departments_stmt = $pdo->query($departments_query);
    $all_departments = $departments_stmt->fetchAll(PDO::FETCH_ASSOC);


    // Handle team assignment form submission
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'assign_team') {
        $team_id = $_POST['team_id'] ?? 0;
        $user_id_to_assign = $_POST['user_id'] ?? 0;

        if ($team_id && $user_id_to_assign) {
            try {
                // Check if user is already in the team
                $check_query = "SELECT COUNT(*) FROM team_members WHERE team_id = :team_id AND user_id = :user_id";
                $check_stmt = $pdo->prepare($check_query);
                $check_stmt->bindParam(':team_id', $team_id);
                $check_stmt->bindParam(':user_id', $user_id_to_assign);
                $check_stmt->execute();

                if ($check_stmt->fetchColumn() > 0) {
                    $error_message = "User is already a member of this team.";
                } else {
                    // Add user to team
                    $insert_query = "INSERT INTO team_members (team_id, user_id) VALUES (:team_id, :user_id)";
                    $insert_stmt = $pdo->prepare($insert_query);
                    $insert_stmt->bindParam(':team_id', $team_id);
                    $insert_stmt->bindParam(':user_id', $user_id_to_assign);
                    $insert_stmt->execute();

                    $success_message = "User successfully assigned to team!";
                    header("Location: index.php?message=" . urlencode($success_message));
                    exit();
                }
            } catch (Exception $e) {
                $error_message = "Error assigning user to team: " . $e->getMessage();
            }
        } else {
            $error_message = "Please select both a user and a team.";
        }
    }

    // Handle team removal form submission
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'remove_from_team') {
        $team_member_id = $_POST['team_member_id'] ?? 0;

        if ($team_member_id) {
            try {
                // Remove user from team
                $delete_query = "DELETE FROM team_members WHERE id = :id";
                $delete_stmt = $pdo->prepare($delete_query);
                $delete_stmt->bindParam(':id', $team_member_id);
                $delete_stmt->execute();

                $success_message = "User successfully removed from team!";
                header("Location: index.php?message=" . urlencode($success_message));
                exit();
            } catch (Exception $e) {
                $error_message = "Error removing user from team: " . $e->getMessage();
            }
        } else {
            $error_message = "Invalid team member selection.";
        }
    }

    // Fetch current team assignments for display
    $team_members_query = "SELECT tm.id, tm.team_id, tm.user_id, 
                          t.name AS team_name, 
                          u.username, u.email, u.role
                          FROM team_members tm
                          JOIN teams t ON tm.team_id = t.id
                          JOIN users u ON tm.user_id = u.id
                          ORDER BY t.name, u.username";
    $team_members_stmt = $pdo->query($team_members_query);
    $team_members = $team_members_stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Add this to the existing System Admin section where we handle form submissions
if ($is_system_admin) {
    // Handle new user creation
    if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'create_user') {
        $new_username = trim($_POST['new_username'] ?? '');
        $new_email = trim($_POST['new_email'] ?? '');
        $new_password = trim($_POST['new_password'] ?? '');
        $new_role = trim($_POST['new_role'] ?? '');
        $new_phone = trim($_POST['new_phone'] ?? '');
        $new_assign_team = $_POST['new_assign_team'] ?? '';
        $new_assign_division = $_POST['new_assign_division'] ?? '';
        $new_assign_department = $_POST['new_assign_department'] ?? '';
        $error_message = '';

        // Validate input
        $validation_errors = [];

        if (empty($new_username)) {
            $validation_errors[] = "Username is required";
        } else {
            // Check if username already exists
            $check_username_query = "SELECT COUNT(*) FROM users WHERE username = :username";
            $check_username_stmt = $pdo->prepare($check_username_query);
            $check_username_stmt->bindParam(':username', $new_username);
            $check_username_stmt->execute();

            if ($check_username_stmt->fetchColumn() > 0) {
                $validation_errors[] = "Username already exists";
            }
        }

        if (empty($new_email)) {
            $validation_errors[] = "Email is required";
        } elseif (!filter_var($new_email, FILTER_VALIDATE_EMAIL)) {
            $validation_errors[] = "Invalid email format";
        } else {
            // Check if email already exists
            $check_email_query = "SELECT COUNT(*) FROM users WHERE email = :email";
            $check_email_stmt = $pdo->prepare($check_email_query);
            $check_email_stmt->bindParam(':email', $new_email);
            $check_email_stmt->execute();

            if ($check_email_stmt->fetchColumn() > 0) {
                $validation_errors[] = "Email already exists";
            }
        }

        if (empty($new_password)) {
            $validation_errors[] = "Password is required";
        } elseif (strlen($new_password) < 6) {
            $validation_errors[] = "Password must be at least 6 characters";
        }

        if (empty($new_role)) {
            $validation_errors[] = "Role is required";
        }

        // If no validation errors, create the user
        if (empty($validation_errors)) {
            try {
                // Hash the password
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

                // Insert the new user
                $insert_user_query = "INSERT INTO users (username, email, password, phone, role,division,department, created_at) 
                                     VALUES (:username, :email, :password, :phone, :role, NOW())";
                $insert_user_stmt = $pdo->prepare($insert_user_query);
                $insert_user_stmt->bindParam(':username', $new_username);
                $insert_user_stmt->bindParam(':email', $new_email);
                $insert_user_stmt->bindParam(':password', $hashed_password);
                $insert_user_stmt->bindParam(':phone', $new_phone);
                $insert_user_stmt->bindParam(':role', $new_role);
                $insert_user_stmt->bindParam(':division', $new_assign_division);
                $insert_user_stmt->bindParam(':department', $new_assign_department);

                if ($insert_user_stmt->execute()) {
                    $new_user_id = $pdo->lastInsertId();

                    // Create default user settings
                    $default_settings_query = "INSERT INTO user_settings (user_id) VALUES (:user_id)";
                    $default_settings_stmt = $pdo->prepare($default_settings_query);
                    $default_settings_stmt->bindParam(':user_id', $new_user_id);
                    $default_settings_stmt->execute();

                    $success_message = "User created successfully!";

                    // Refresh the users list
                    $users_stmt = $pdo->query($users_query);
                    $all_users = $users_stmt->fetchAll(PDO::FETCH_ASSOC);
                    // After successfully creating the user
                    if (!empty($new_user_id) && !empty($_POST['new_assign_team'])) {
                        $team_id_to_assign = $_POST['new_assign_team'];

                        // Add user to team
                        try {
                            $insert_team_query = "INSERT INTO team_members (team_id, user_id) VALUES (:team_id, :user_id)";
                            $insert_team_stmt = $pdo->prepare($insert_team_query);
                            $insert_team_stmt->bindParam(':team_id', $team_id_to_assign);
                            $insert_team_stmt->bindParam(':user_id', $new_user_id);
                            $insert_team_stmt->execute();

                            $success_message .= " User was also assigned to the selected team.";
                            header("Location: index.php?message=" . urlencode($success_message));
                            exit();

                            // Refresh team members list
                            //$team_members_stmt = $pdo->query($team_members_query);
                            //$team_members = $team_members_stmt->fetchAll(PDO::FETCH_ASSOC);
                        } catch (Exception $e) {
                            $error_message = "User created but could not be assigned to team: " . $e->getMessage();
                        }
                    }
                } else {
                    $error_message = "Error creating user";
                }
            } catch (Exception $e) {
                $error_message = "Error creating user: " . $e->getMessage();
            }
        } else {
            $error_message = "Please correct the following errors: " . implode(", ", $validation_errors);
        }
    }
}

// Add this to your existing form handling section
if ($is_system_admin && $_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'edit_user') {
    $edit_user_id = $_POST['edit_user_id'];
    $edit_username = trim($_POST['edit_username']);
    $edit_email = trim($_POST['edit_email']);
    $edit_role = trim($_POST['edit_role']);
    $edit_phone = trim($_POST['edit_phone']);
    $edit_password = trim($_POST['edit_password']);
    $edit_division = $_POST['edit_assign_division'] ?? '';
    $edit_department = $_POST['edit_assign_department'] ?? '';

    try {
        // Start building the update query
        $update_fields = [];
        $params = [':user_id' => $edit_user_id];

        // Add fields to update
        if (!empty($edit_username)) {
            $update_fields[] = "username = :username";
            $params[':username'] = $edit_username;
        }
        if (!empty($edit_email)) {
            $update_fields[] = "email = :email";
            $params[':email'] = $edit_email;
        }
        if (!empty($edit_role)) {
            $update_fields[] = "role = :role";
            $params[':role'] = $edit_role;
        }
        if (!empty($edit_phone)) {
            $update_fields[] = "phone = :phone";
            $params[':phone'] = $edit_phone;
        }
        if (!empty($edit_password)) {
            $update_fields[] = "password = :password";
            $params[':password'] = password_hash($edit_password, PASSWORD_DEFAULT);
        }
        if (!empty($edit_division)) {
            $update_fields[] = "division = :division";
            $params[':division'] = $edit_division;
        }
        if (!empty($edit_department)) {
            $update_fields[] = "department = :department";
            $params[':department'] = $edit_department;
        }

        if (!empty($update_fields)) {

            $pdo->beginTransaction();
            $update_query = "UPDATE users SET " . implode(", ", $update_fields) . " WHERE id = :user_id";

            $update_stmt = $pdo->prepare($update_query);
            $update_stmt->execute($params);

            $success_message = "User updated successfully!";
            $pdo->commit();

            header("Location: index.php?message=" . urlencode($success_message));
            exit();
            // Refresh the users list
            //$users_stmt = $pdo->query($users_query);
            //$all_users = $users_stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    } catch (Exception $e) {
        $error_message = "Error updating user: " . $e->getMessage();
    }
}

if ($is_system_admin && $_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'add_cause_template') {
    //INSERT INTO ticket_cause_template(main_cause,sub_cause,issue_cause,group_cause,cause_name,cause_name_en) VALUES 

    $main_cause = $_POST['main_cause'] ?? '';
    $sub_cause = $_POST['sub_cause'] ?? '';
    $issue_cause = $_POST['issue_cause'] ?? '';
    $group_cause = $_POST['group_cause'] ?? '';
    $cause_name = $_POST['cause_name'] ?? '';
    $cause_name_en = $_POST['cause_name_en'] ?? '';
    $error_message = '';

    if (empty($main_cause) || empty($sub_cause) || empty($issue_cause) || empty($group_cause) || empty($cause_name) || empty($cause_name_en)) {
        $error_message = "All fields are required.";
    } else {
        try {
            $insert_query = "INSERT INTO ticket_cause_template (main_cause, sub_cause, issue_cause, group_cause, cause_name, cause_name_en) 
                             VALUES (:main_cause, :sub_cause, :issue_cause, :group_cause, :cause_name, :cause_name_en)";

            $insert_stmt = $pdo->prepare($insert_query);
            $insert_stmt->bindParam(':main_cause', $main_cause);
            $insert_stmt->bindParam(':sub_cause', $sub_cause);
            $insert_stmt->bindParam(':issue_cause', $issue_cause);
            $insert_stmt->bindParam(':group_cause', $group_cause);
            $insert_stmt->bindParam(':cause_name', $cause_name);
            $insert_stmt->bindParam(':cause_name_en', $cause_name_en);
            if ($insert_stmt->execute()) {
                $success_message = "Cause template added successfully!";
                header("Location: index.php?message=" . urlencode($success_message));
                exit();
            } else {
                $error_message = "Error adding cause template.";
            }
        } catch (Exception $e) {
            $error_message = "Error adding cause template: " . $e->getMessage();
        }
    }
}

require_once '../../includes/header.php';
?>
<style>
    #causeTemplatesTable {
        table-layout: fixed;
        /* allow DataTables to respect width */
        width: 100%;
    }

    #causeTemplatesTable th:last-child,
    #causeTemplatesTable td:last-child {
        width: 80px;
        /* desired Actions column width */
        max-width: 100px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
</style>
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">User Settings</h4>
                </div>
                <div class="card-body small">
                    <?php
                    $message = isset($_GET['message']) ? $_GET['message'] : '';
                    if (!empty($message)): ?>
                        <div class="alert alert-success"><?php echo htmlspecialchars(urldecode($message)); ?></div>
                    <?php endif; ?>
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger"><?php echo htmlspecialchars($error_message); ?></div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <input type="hidden" name="action" value="update_settings">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <h5>User Information</h5>
                                <div class="form-group">
                                    <label>Username:</label>
                                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($userdata['username']); ?>" readonly>
                                </div>
                                <div class="form-group">
                                    <label>Role:</label>
                                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($userdata['role']); ?>" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h5>Notification Preferences</h5>
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="email_notifications"
                                            name="email_notifications" <?php echo @$settings['email_notifications'] ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="email_notifications">Email Notifications</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="sms_notifications"
                                            name="sms_notifications" <?php echo @$settings['sms_notifications'] ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="sms_notifications">SMS Notifications</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="system_notifications"
                                            name="system_notifications" <?php echo @$settings['system_notifications'] ? 'checked' : ''; ?>>
                                        <label class="custom-control-label" for="system_notifications">System Notifications</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row d-none">
                            <div class="col-md-6">
                                <h5>Display Settings</h5>
                                <div class="form-group">
                                    <label for="theme">Theme:</label>
                                    <select class="form-control form-control_sm" id="theme" name="theme">
                                        <option value="light" <?php echo (@$settings['theme'] == 'light') ? 'selected' : ''; ?>>Light</option>
                                        <option value="dark" <?php echo (@$settings['theme'] == 'dark') ? 'selected' : ''; ?>>Dark</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="language">Language:</label>
                                    <select class="form-control form-control_sm" id="language" name="language">
                                        <option value="en" <?php echo (@$settings['language'] == 'en') ? 'selected' : ''; ?>>English</option>
                                        <option value="th" <?php echo (@$settings['language'] == 'th') ? 'selected' : ''; ?>>Thai</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="items_per_page">Items per page:</label>
                                    <select class="form-control form-control_sm" id="items_per_page" name="items_per_page">
                                        <option value="10" <?php echo (@$settings['items_per_page'] == 10) ? 'selected' : ''; ?>>10</option>
                                        <option value="25" <?php echo (@$settings['items_per_page'] == 25) ? 'selected' : ''; ?>>25</option>
                                        <option value="50" <?php echo (@$settings['items_per_page'] == 50) ? 'selected' : ''; ?>>50</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Save Changes
                            </button>
                            <a href="../dashboard/index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Back to Dashboard
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if ($is_system_admin): ?>
    <div class="row mt-5">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">Management and Assignment</h4>
                </div>
                <div class="card-body small">
                    <ul class="nav nav-tabs" id="teamManagementTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="assign-tab" data-toggle="tab" href="#assign" role="tab">
                                <i class="fas fa-user-plus"></i> Assign Users to Teams
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="current-tab" data-toggle="tab" href="#current" role="tab">
                                <i class="fas fa-users"></i> Current Team Members
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="new-user-tab" data-toggle="tab" href="#new-user" role="tab">
                                <i class="fas fa-user-plus"></i> Create New User
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="cause-tab" data-toggle="tab" href="#cause_of_failure" role="tab">
                                <i class="fas fa-exclamation-triangle"></i> Causes Of Failure
                            </a>
                        </li>
                    </ul>

                    <div class="tab-content mt-3" id="teamManagementTabContent">
                        <!-- Assign Users to Teams Tab -->
                        <div class="tab-pane mb-5 fade show active" id="assign" role="tabpanel">
                            <form method="POST" action="">
                                <input type="hidden" name="action" value="assign_team">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="user_id"><i class="fas fa-user"></i> Select User:</label>
                                            <select class="form-control" id="user_id" name="user_id" required>
                                                <option value="">-- Select User --</option>
                                                <?php foreach ($all_users as $user): ?>
                                                    <option value="<?php echo $user['id']; ?>">
                                                        <?php echo htmlspecialchars($user['fullname'] ?? '-') . '(' . htmlspecialchars($user['username']) . ')'; ?>
                                                        (<?php echo htmlspecialchars($user['department']); ?>)
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label for="team_id"><i class="fas fa-users"></i> Select Team:</label>
                                            <select class="form-control" id="team_id" name="team_id" required>
                                                <option value="">-- Select Team --</option>
                                                <?php foreach ($all_teams as $team): ?>
                                                    <option value="<?php echo $team['id']; ?>">
                                                        <?php echo htmlspecialchars($team['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-1 d-flex align-items-end mb-2">
                                        <button type="submit" class="btn btn-success btn-block">
                                            <i class="fas fa-plus-circle"></i> Assign
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Current Team Members Tab -->
                        <div class="tab-pane fade" id="current" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Team</th>
                                            <th>Username</th>
                                            <th>Email</th>

                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (!empty($team_members)): ?>
                                            <?php foreach ($team_members as $member): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($member['team_name']); ?></td>
                                                    <td><?php echo htmlspecialchars($member['username']); ?></td>
                                                    <td><?php echo htmlspecialchars($member['email']); ?></td>

                                                    <td>
                                                        <form method="POST" action="" class="d-inline"
                                                            onsubmit="return confirm('Are you sure you want to remove this user from the team?');">
                                                            <input type="hidden" name="action" value="remove_from_team">
                                                            <input type="hidden" name="team_member_id" value="<?php echo $member['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-danger">
                                                                <i class="fas fa-user-minus"></i> Remove
                                                            </button>
                                                        </form>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="5" class="text-center">No team assignments found.</td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Create New User Tab -->
                        <div class="tab-pane fade" id="new-user" role="tabpanel" aria-labelledby="new-user-tab">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0">Create New User</h5>
                                </div>
                                <div class="card-body">
                                    <form method="POST" action="" id="createUserForm">
                                        <input type="hidden" name="action" value="create_user">

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="new_username"><i class="fas fa-user"></i> Username:</label>
                                                    <input type="text" class="form-control" id="new_username" name="new_username" required>
                                                    <small class="form-text text-muted">Username must be unique</small>
                                                </div>

                                                <div class="form-group">
                                                    <label for="new_email"><i class="fas fa-envelope"></i> Email:</label>
                                                    <input type="email" class="form-control" id="new_email" name="new_email" required>
                                                </div>

                                                <div class="form-group">
                                                    <label for="new_password"><i class="fas fa-lock"></i> Password:</label>
                                                    <div class="input-group">
                                                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                                                        <div class="input-group-append">
                                                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <small class="form-text text-muted">Password must be at least 6 characters</small>
                                                </div>
                                                <div class="form-group">
                                                    <label for="new_assign_division"><i class="fas fa-object-group"></i> Division:</label>
                                                    <select class="form-control" id="new_assign_division" name="new_assign_division">
                                                        <option value="">-- None --</option>
                                                        <?php foreach ($all_divisions as $division): ?>
                                                            <option value="<?php echo $team['name']; ?>">
                                                                <?php echo htmlspecialchars($division['name']); ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                    <small class="form-text text-muted">Division -> Department -> Team</small>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="new_role"><i class="fas fa-user-tag"></i> Role:</label>
                                                    <select class="form-control" id="new_role" name="new_role" required>
                                                        <option value="">-- Select Role --</option>
                                                        <option value="admin">Administrator</option>
                                                        <option value="technician">Technician</option>
                                                        <option value="customer_service">Customer Service</option>

                                                    </select>
                                                </div>

                                                <div class="form-group">
                                                    <label for="new_phone"><i class="fas fa-phone"></i> Phone Number:</label>
                                                    <input type="text" class="form-control" id="new_phone" name="new_phone">
                                                    <small class="form-text text-muted">Optional</small>
                                                </div>

                                                <div class="form-group">
                                                    <label for="new_assign_team"><i class="fas fa-users"></i> Assign to Team:</label>
                                                    <select class="form-control" id="new_assign_team" name="new_assign_team">
                                                        <option value="">-- None --</option>
                                                        <?php foreach ($all_teams as $team): ?>
                                                            <option value="<?php echo $team['id']; ?>">
                                                                <?php echo htmlspecialchars($team['name']); ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                    <small class="form-text text-muted">Optional - You can assign the user to a team later</small>
                                                </div>
                                                <div class="form-group">
                                                    <label for="new_assign_department"><i class="fas fa-object-group"></i> Department:</label>
                                                    <select class="form-control" id="new_assign_department" name="new_assign_department">
                                                        <option value="">-- None --</option>
                                                        <?php foreach ($all_departments as $department): ?>
                                                            <option value="<?php echo $team['name']; ?>">
                                                                <?php echo htmlspecialchars($department['name']); ?>
                                                            </option>
                                                        <?php endforeach; ?>
                                                    </select>
                                                    <small class="form-text text-muted">Division -> Department -> Team</small>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group mt-3">
                                            <button type="submit" class="btn btn-success">
                                                <i class="fas fa-user-plus"></i> Create User
                                            </button>
                                            <button type="reset" class="btn btn-secondary">
                                                <i class="fas fa-undo"></i> Reset Form
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- User List -->
                            <div class="mt-4">
                                <h5>Existing Users</h5>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover" id="usersTable">
                                        <thead>
                                            <tr>
                                                <th>Username</th>
                                                <th>Email</th>
                                                <th>Role</th>
                                                <th>Division</th>
                                                <th>Department</th>
                                                <th>Phone</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($all_users as $user): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                                    <td><?php echo htmlspecialchars($user['role']); ?></td>
                                                    <td><?php echo htmlspecialchars($user['division'] ?? 'N/A'); ?></td>
                                                    <td><?php echo htmlspecialchars($user['department'] ?? 'N/A'); ?></td>
                                                    <td><?php echo htmlspecialchars($user['phone'] ?? 'N/A'); ?></td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-user"
                                                            data-user-id="<?php echo $user['id']; ?>"
                                                            data-username="<?php echo htmlspecialchars($user['username']); ?>"
                                                            data-email="<?php echo htmlspecialchars($user['email']); ?>"
                                                            data-role="<?php echo htmlspecialchars($user['role']); ?>"
                                                            data-division="<?php echo htmlspecialchars($user['division'] ?? ''); ?>"
                                                            data-department="<?php echo htmlspecialchars($user['department'] ?? ''); ?>"
                                                            data-phone="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>"
                                                            data-toggle="modal" data-target="#editUserModal">
                                                            <i class="fas fa-edit"></i> Edit
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <!-- Causes Of Failure Tab -->
                        <div class="tab-pane fade" id="cause_of_failure" role="tabpanel" aria-labelledby="cause-tab">
                            <div class="card border-danger mt-3">
                                <div class="card-header bg-danger text-white">
                                    <h5 class="mb-0">Causes Of Failure</h5>
                                </div>
                                <div class="card-body">
                                    <form id="causeOfFailureForm" method="POST" action="">
                                        <input type="hidden" name="action" value="add_cause_template">
                                        <div class="form-row">
                                            <div class="form-group col-md-3">
                                                <label for="main_cause">Main Cause</label>
                                                <div class="input-group">
                                                    <select class="form-control" id="main_cause" name="main_cause" required>
                                                        <option value="">Select Main Cause</option>
                                                        <?php
                                                        $main_causes = $pdo->query("SELECT title FROM ticket_main_cause ORDER BY title")->fetchAll(PDO::FETCH_COLUMN);
                                                        foreach ($main_causes as $main) {
                                                            echo '<option value="' . htmlspecialchars($main) . '">' . htmlspecialchars($main) . '</option>';
                                                        }
                                                        ?>
                                                    </select>
                                                    <div class="input-group-append">
                                                        <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#addMainCauseModal">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-warning btn-sm" data-toggle="modal" data-target="#editMainCauseModal">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group col-md-3">
                                                <label for="sub_cause">Sub Cause</label>
                                                <div class="input-group">
                                                    <select class="form-control" id="sub_cause" name="sub_cause" required>
                                                        <option value="">Select Sub Cause</option>
                                                        <?php
                                                        $sub_causes = $pdo->query("SELECT title FROM ticket_sub_cause ORDER BY title")->fetchAll(PDO::FETCH_COLUMN);
                                                        foreach ($sub_causes as $sub) {
                                                            echo '<option value="' . htmlspecialchars($sub) . '">' . htmlspecialchars($sub) . '</option>';
                                                        }
                                                        ?>
                                                    </select>
                                                    <div class="input-group-append">
                                                        <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#addSubCauseModal">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-warning btn-sm" data-toggle="modal" data-target="#editSubCauseModal">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group col-md-3">
                                                <label for="issue_cause">Issue Cause</label>
                                                <div class="input-group">
                                                    <select class="form-control" id="issue_cause" name="issue_cause" required>
                                                        <option value="">Select Issue Cause</option>
                                                        <?php
                                                        $issue_causes = $pdo->query("SELECT title FROM ticket_issue_cause ORDER BY title")->fetchAll(PDO::FETCH_COLUMN);
                                                        foreach ($issue_causes as $issue) {
                                                            echo '<option value="' . htmlspecialchars($issue) . '">' . htmlspecialchars($issue) . '</option>';
                                                        }
                                                        ?>
                                                    </select>
                                                    <div class="input-group-append">
                                                        <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#addIssueCauseModal">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-warning btn-sm" data-toggle="modal" data-target="#editIssueCauseModal">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="form-group col-md-3">
                                                <label for="group_cause">Group Cause</label>
                                                <div class="input-group">
                                                    <select class="form-control" id="group_cause" name="group_cause" required>
                                                        <option value="">Select Group Cause</option>
                                                        <?php
                                                        $group_causes = $pdo->query("SELECT title FROM ticket_group_cause ORDER BY title")->fetchAll(PDO::FETCH_COLUMN);
                                                        foreach ($group_causes as $group) {
                                                            echo '<option value="' . htmlspecialchars($group) . '">' . htmlspecialchars($group) . '</option>';
                                                        }
                                                        ?>
                                                    </select>
                                                    <div class="input-group-append">
                                                        <button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#addGroupCauseModal">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-warning btn-sm" data-toggle="modal" data-target="#editGroupCauseModal">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-row mt-3">
                                            <div class="form-group col-md-6">
                                                <label for="cause_name">Cause Description</label>
                                                <textarea class="form-control mt-2" id="cause_name" name="cause_name" rows="3" placeholder="Enter Cause Description" required></textarea>
                                            </div>
                                            <div class="form-group col-md-6">
                                                <label for="cause_name_en">Cause Description (English)</label>
                                                <textarea class="form-control mt-2" id="cause_name_en" name="cause_name_en" rows="3" placeholder="Enter Cause Description In English" required></textarea>
                                            </div>
                                        </div>
                                        <div class="form-group mt-3">
                                            <button type="submit" class="btn btn-success">
                                                <i class="fas fa-plus"></i> Add Cause Template
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Cause Templates Table list -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="mb-0">Cause Templates</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table id="causeTemplatesTable" name="causeTemplatesTable" class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Main Cause</th>
                                                    <th>Sub Cause</th>
                                                    <th>Issure Cause</th>
                                                    <th>Group Cause</th>
                                                    <th>Cause Description</th>
                                                    <th>Status</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php
                                                $cause_templates = $pdo->query("SELECT * FROM ticket_cause_template ORDER BY main_cause,sub_cause")->fetchAll(PDO::FETCH_ASSOC);
                                                foreach ($cause_templates as $template):
                                                ?>
                                                    <tr>
                                                        <td><?php echo htmlspecialchars($template['main_cause']); ?></td>
                                                        <td><?php echo htmlspecialchars($template['sub_cause']); ?></td>
                                                        <td><?php echo htmlspecialchars($template['issue_cause']); ?></td>
                                                        <td><?php echo htmlspecialchars($template['group_cause']); ?></td>
                                                        <td><?php echo htmlspecialchars($template['cause_name']); ?></td>
                                                        <td class="text-center">
                                                            <div class="custom-control custom-switch">
                                                                <input type="checkbox" class="custom-control-input status-toggle"
                                                                    id="status_<?php echo $template['id']; ?>"
                                                                    data-template-id="<?php echo $template['id']; ?>"
                                                                    <?php echo ($template['status'] ?? 1) == 1 ? 'checked' : ''; ?>>
                                                                <label class="custom-control-label" for="status_<?php echo $template['id']; ?>"></label>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-primary edit-cause-template"
                                                                data-template-id="<?php echo $template['id']; ?>"
                                                                data-cause-name="<?php echo htmlspecialchars($template['cause_name']); ?>"
                                                                data-cause-name-en="<?php echo htmlspecialchars($template['cause_name_en']); ?>"
                                                                data-toggle="modal" data-target="#editCauseTemplateModal">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-danger delete-cause-template"
                                                                data-template-id="<?php echo $template['id']; ?>">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>
<!-- Add this after the users table -->
<div class="modal fade" id="editUserModal" tabindex="-1" role="dialog" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editUserModalLabel">Edit User</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="POST" action="" id="editUserForm">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit_user">
                    <input type="hidden" name="edit_user_id" id="edit_user_id">

                    <div class="form-group">
                        <label for="edit_username"><i class="fas fa-user"></i> Username:</label>
                        <input type="text" class="form-control" id="edit_username" name="edit_username" required>
                    </div>

                    <div class="form-group">
                        <label for="edit_email"><i class="fas fa-envelope"></i> Email:</label>
                        <input type="email" class="form-control" id="edit_email" name="edit_email" required>
                    </div>
                    <div class="form-group">
                        <label for="edit_assign_division"><i class="fas fa-object-group"></i> Division:</label>
                        <select class="form-control" id="edit_assign_division" name="edit_assign_division">
                            <option value="">-- None --</option>
                            <?php foreach ($all_divisions as $division): ?>
                                <option value="<?php echo $division['name']; ?>">
                                    <?php echo htmlspecialchars($division['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="edit_assign_department"><i class="fas fa-object-group"></i> Department:</label>
                        <select class="form-control" id="edit_assign_department" name="edit_assign_department">
                            <option value="">-- None --</option>
                            <?php foreach ($all_departments as $department): ?>
                                <option value="<?php echo $department['name']; ?>">
                                    <?php echo htmlspecialchars($department['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="edit_role"><i class="fas fa-user-tag"></i> Role:</label>
                        <select class="form-control" id="edit_role" name="edit_role" required>
                            <option value="admin">Administrator</option>
                            <option value="technician">Technician</option>
                            <option value="customer_service">Customer Service</option>

                        </select>
                    </div>

                    <div class="form-group">
                        <label for="edit_phone"><i class="fas fa-phone"></i> Phone:</label>
                        <input type="text" class="form-control" id="edit_phone" name="edit_phone">
                    </div>

                    <div class="form-group">
                        <label for="edit_password"><i class="fas fa-lock"></i> New Password:</label>
                        <input type="password" class="form-control" id="edit_password" name="edit_password">
                        <small class="form-text text-muted">Leave blank to keep current password</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- Add/Edit Main Cause Modals -->
<div class="modal fade" id="addMainCauseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Main Cause</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="addMainCauseForm">
                    <div class="form-group">
                        <label>Main Cause Title:</label>
                        <input type="text" class="form-control" name="title" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="addCause('main')">Add</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="editMainCauseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Main Cause</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="editMainCauseForm">
                    <div class="form-group">
                        <label>Select Main Cause:</label>
                        <select class="form-control" id="editMainCauseSelect" required></select>
                    </div>
                    <div class="form-group">
                        <label>New Title:</label>
                        <input type="text" class="form-control" name="new_title" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="updateCause('main')">Update</button>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Sub Cause Modals -->
<div class="modal fade" id="addSubCauseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Sub Cause</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="addSubCauseForm">
                    <div class="form-group">
                        <label>Sub Cause Title:</label>
                        <input type="text" class="form-control" name="title" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="addCause('sub')">Add</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="editSubCauseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Sub Cause</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="editSubCauseForm">
                    <div class="form-group">
                        <label>Select Sub Cause:</label>
                        <select class="form-control" id="editSubCauseSelect" required></select>
                    </div>
                    <div class="form-group">
                        <label>New Title:</label>
                        <input type="text" class="form-control" name="new_title" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="updateCause('sub')">Update</button>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Issue Cause Modals -->
<div class="modal fade" id="addIssueCauseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Issue Cause</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="addIssueCauseForm">
                    <div class="form-group">
                        <label>Issue Cause Title:</label>
                        <input type="text" class="form-control" name="title" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="addCause('issue')">Add</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="editIssueCauseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Issue Cause</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="editIssueCauseForm">
                    <div class="form-group">
                        <label>Select Issue Cause:</label>
                        <select class="form-control" id="editIssueCauseSelect" required></select>
                    </div>
                    <div class="form-group">
                        <label>New Title:</label>
                        <input type="text" class="form-control" name="new_title" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="updateCause('issue')">Update</button>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Group Cause Modals -->
<div class="modal fade" id="addGroupCauseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Group Cause</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="addGroupCauseForm">
                    <div class="form-group">
                        <label>Group Cause Title:</label>
                        <input type="text" class="form-control" name="title" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="addCause('group')">Add</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="editGroupCauseModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Group Cause</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="editGroupCauseForm">
                    <div class="form-group">
                        <label>Select Group Cause:</label>
                        <select class="form-control" id="editGroupCauseSelect" required></select>
                    </div>
                    <div class="form-group">
                        <label>New Title:</label>
                        <input type="text" class="form-control" name="new_title" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="updateCause('group')">Update</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Cause Template Modal -->
<div class="modal fade" id="editCauseTemplateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Cause Template</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>

            <div class="modal-body">
                <input type="hidden" name="action" value="edit_cause_template">
                <input type="hidden" name="template_id" id="edit_template_id">

                <div class="form-group">
                    <label for="edit_cause_name">Cause Description:</label>
                    <textarea class="form-control" id="edit_cause_name" name="cause_name" rows="3" required></textarea>
                </div>

                <div class="form-group">
                    <label for="edit_cause_name_en">Cause Description (English):</label>
                    <textarea class="form-control" id="edit_cause_name_en" name="cause_name_en" rows="3" required></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" onclick="editCauseTemplate()">Update</button>
            </div>

        </div>
    </div>
</div>

<?php
// Include main script loader
// This is where you can include any additional scripts or stylesheets
require_once '../../includes/main_script_loader.php';
// Add this before the closing </body> tag
if ($is_system_admin) {
    echo '<script src="' . BASE_URL . '/assets/js/team-management.js?v=' . time() . '"></script>';
}

?>
<script>
    $(document).ready(function() {
        // Destroy if already initialized (safe re-init)
        var $ct = $('#causeTemplatesTable');
        if ($.fn.dataTable.isDataTable($ct)) {
            $ct.DataTable().destroy();
            $ct.find('thead th').removeClass('sorting_asc sorting_desc sorting');
        }

        $ct.DataTable({
            paging: true,
            pageLength: 100,
            order: [
                [0, 'desc']
            ],
            autoWidth: false, // important to let columnDefs widths apply
            columnDefs: [
                // Actions column is the last column (-1)
                {
                    targets: -1,
                    width: '80px',
                    orderable: false,
                    className: 'text-center'
                },
                // Status column is the second to last column (-2)
                {
                    targets: -2,
                    width: '80px',
                    orderable: false,
                    className: 'text-center'
                },
            ],
            language: {
                search: 'Search:',
                lengthMenu: 'Show _MENU_ per page',
                info: 'Showing _START_ to _END_ of _TOTAL_ Templates'
            }
        });
        $('#user_id').select2({
            width: '100%',
            placeholder: 'Select a user',
            allowClear: true
        });
        $('#team_id').select2({
            width: '100%',
            placeholder: 'Select a team',
            allowClear: true
        });
    });

    // Status toggle functionality for cause templates
    $(document).on('change', '.status-toggle', function() {
        const templateId = $(this).data('template-id');
        const status = $(this).is(':checked') ? 1 : 0;
        const toggleSwitch = $(this);

        $.ajax({
            url: 'toggle_cause_status.php',
            method: 'POST',
            data: {
                template_id: templateId,
                status: status
            },
            success: function(response) {
                if (response.success) {
                    // Show success message
                    toastr.options = {
                    "closeButton": true,
                    "progressBar": true,
                    "positionClass": "toast-top-right",
                    "timeOut": "2000"
                    };
                    toastr.success('Status updated successfully');

                } else {
                    // Revert toggle on error
                    toggleSwitch.prop('checked', !toggleSwitch.is(':checked'));
                    toastr.error('Error updating status: ' + result.message);
                }
            },
            error: function() {
                // Revert toggle on error
                toggleSwitch.prop('checked', !toggleSwitch.is(':checked'));
                toastr.error('Error updating status');
            }
        });
    });

    $('.edit-user').click(function() {
        //alert('edit user clicked');
        let userId = $(this).data('user-id');
        let username = $(this).data('username');
        let email = $(this).data('email');
        let role = $(this).data('role');
        let phone = $(this).data('phone');
        //console.log('user id:',userId,'username:',username,'email:',email,'role:',role,'phone:',phone);

        $('#edit_user_id').val(userId);
        $('#edit_username').val(username);
        $('#edit_email').val(email);
        $('#edit_role').val(role);
        $('#edit_assign_division').val($(this).data('division') || '');
        $('#edit_assign_department').val($(this).data('department') || '');
        $('#edit_phone').val(phone);
        $('#edit_password').val(''); // Clear password field
    });

    // Add this script for cause template deletion
    $('.delete-cause-template').click(function() {
        if (confirm('Are you sure you want to delete this cause template?')) {
            let templateId = $(this).data('template-id');

            $.ajax({
                url: 'delete_cause_template.php',
                type: 'POST',
                data: {
                    id: templateId
                },
                success: function(response) {
                    if (response.success) {
                        toastr.info('Cause template deleted successfully.');
                        setTimeout(() => { location.reload();}, 3000);
                    } else {
                        toastr.error('Error deleting cause template: ' + response.message);
                    }
                },
                error: function() {
                    toastr.error('An error occurred while processing your request.');
                }
            });
        }
    });

    // Edit cause template click handler
    $('.edit-cause-template').click(function() {
        let templateId = $(this).data('template-id');
        let causeName = $(this).data('cause-name');
        let causeNameEn = $(this).data('cause-name-en');

        $('#edit_template_id').val(templateId);
        $('#edit_cause_name').val(causeName);
        $('#edit_cause_name_en').val(causeNameEn);
    });

    function editCauseTemplate() {
        let templateId = $('#edit_template_id').val();
        let causeName = $('#edit_cause_name').val();
        let causeNameEn = $('#edit_cause_name_en').val();
        $.ajax({
            url: 'manage_causes.php',
            type: 'POST',
            data: {
                action: 'edit_cause_template',
                type: 'cause_template',
                template_id: templateId,
                cause_name: causeName,
                cause_name_en: causeNameEn
            },
            success: function(response) {
                if (response.success) {
                    toastr.success('Cause template updated successfully.');
                    setTimeout(() => { location.reload();}, 3000);
                } else {
                    toastr.error('Error updating cause template: ' + response.message);
                }
            },
            error: function() {
                toastr.error('An error occurred while processing your request.');
            }
        });
    }

    function addCause(type) {
        const title = $(`#add${type.charAt(0).toUpperCase() + type.slice(1)}CauseForm input[name="title"]`).val();
        if (!title) return;

        $.ajax({
            url: 'manage_causes.php',
            type: 'POST',
            data: {
                action: 'add',
                type: type,
                title: title
            },
            success: function(response) {
                if (response.success) {
                    // Add to dropdown
                    $(`#${type}_cause`).append(`<option value="${title}">${title}</option>`);
                    $(`#add${type.charAt(0).toUpperCase() + type.slice(1)}CauseModal`).modal('hide');
                    $(`#add${type.charAt(0).toUpperCase() + type.slice(1)}CauseForm`)[0].reset();
                    toastr.success('Added successfully!');
                } else {
                    toastr.error('Error: ' + response.message);
                }
            },
            error: function() {
                toastr.error('An error occurred while processing your request.');
            }
        });
    }

    function updateCause(type) {
        const oldTitle = $(`#edit${type.charAt(0).toUpperCase() + type.slice(1)}CauseSelect`).val();
        const newTitle = $(`#edit${type.charAt(0).toUpperCase() + type.slice(1)}CauseForm input[name="new_title"]`).val();
        if (!oldTitle || !newTitle) return;

        $.ajax({
            url: 'manage_causes.php',
            type: 'POST',
            data: {
                action: 'update',
                type: type,
                old_title: oldTitle,
                new_title: newTitle
            },
            success: function(response) {
                if (response.success) {
                    // Update dropdown
                    $(`#${type}_cause option[value="${oldTitle}"]`).val(newTitle).text(newTitle);
                    $(`#edit${type.charAt(0).toUpperCase() + type.slice(1)}CauseModal`).modal('hide');
                    toastr.success('Updated successfully!');
                } else {
                    toastr.error('Error: ' + response.message);
                }
            }
        });
    }

    // Populate edit modals
    $('.modal[id^="edit"]').on('show.bs.modal', function() {
        const type = $(this).attr('id').replace('edit', '').replace('CauseModal', '').toLowerCase();
        const editSelect = $(`#edit${type.charAt(0).toUpperCase() + type.slice(1)}CauseSelect`);
        editSelect.empty();

        $(`#${type}_cause option:not(:first)`).each(function() {
            editSelect.append(`<option value="${$(this).val()}">${$(this).text()}</option>`);
        });
    });
</script>

<?php
require_once '../../includes/footer.php';
?>
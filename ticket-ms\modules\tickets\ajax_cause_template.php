<?php
header('Content-Type: application/json');
require_once '../../config/defined.conf.php';
require_once '../../includes/authenCheck.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

$search = $_GET['search'] ?? '';
$limit = 100;
$where = 'AND cause_name LIKE :search';
if ($search === '%') {
        $limit = 1000;
}

$sql = "SELECT id, cause_name, cause_name_en 
        FROM ticket_cause_template 
        WHERE status = 1 
        $where
        ORDER BY cause_name 
        LIMIT $limit";

$stmt = $pdo->prepare($sql);
if ($search === '%') {
    $stmt->bindValue(':search', '%');
} else {
    $stmt->bindValue(':search', "%$search%");
}
$stmt->execute();

$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo json_encode($results);

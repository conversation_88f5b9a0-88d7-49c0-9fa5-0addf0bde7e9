CREATE TABLE `ticket_main_cause` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`title` TINYTEXT NOT NULL COLLATE 'utf8mb4_general_ci',
	`update_at` TIMESTAMP NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `title` (`title`(255)) USING BTREE
)

CREATE TABLE `ticket_sub_cause` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`title` TINYTEXT NOT NULL COLLATE 'utf8mb4_general_ci',
	`update_at` TIMESTAMP NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `title` (`title`(255)) USING BTREE
)

CREATE TABLE `ticket_issue_cause` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`title` TINYTEXT NOT NULL COLLATE 'utf8mb4_general_ci',
	`update_at` TIMESTAMP NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `title` (`title`(255)) USING BTREE
)

CREATE TABLE `ticket_group_cause` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`title` TINYTEXT NOT NULL COLLATE 'utf8mb4_general_ci',
	`update_at` TIMESTAMP NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `title` (`title`(255)) USING BTREE
)

CREATE TABLE `ticket_cause_template` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`main_cause` TINYTEXT NOT NULL COLLATE 'utf8mb4_general_ci',
	`sub_cause` TINYTEXT NOT NULL COLLATE 'utf8mb4_general_ci',
	`issure_cause` TINYTEXT NOT NULL COLLATE 'utf8mb4_general_ci',
	`group_cause` TINYTEXT NOT NULL COLLATE 'utf8mb4_general_ci',
	`cause_name` MEDIUMTEXT NOT NULL COLLATE 'utf8mb4_general_ci',
	`cause_name_en` MEDIUMTEXT NOT NULL COLLATE 'utf8mb4_general_ci',
	`status` INT(11) NOT NULL DEFAULT '1' COMMENT '0-inactivem 1=active',
	`update_at` TIMESTAMP NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `main_cause` (`main_cause`(255)) USING BTREE,
	INDEX `sub_cause` (`sub_cause`(255)) USING BTREE,
	INDEX `issure_cause` (`issure_cause`(255)) USING BTREE,
	INDEX `group_cause` (`group_cause`(255)) USING BTREE,
	INDEX `status` (`status`) USING BTREE
)


WITH TmpOnHold AS (
SELECT ticket_number,changed_at FROM  `ticket_management`.`ticket_status_history` 
			WHERE 
			 `status`='Pending' 
)

SELECT 
       `t`.`ticket_number` AS `ticket_number`,
       `t`.`customer_number` AS `customer_number`,
       `t`.`csno` AS `csno`,
       `pj`.`Project_Name` AS `project_name`,
       `pt`.`Connection_Type` AS `producttype`,
       `m`.Login AS `login_name`,
       `t`.`issue_details` AS `issue_details`,
       `t`.`priority` AS `priority`,
       `t`.`status` AS `status`,
       `t`.`assigned_worker` AS assigned_staff,
       `t`.`created_at` AS `created_at`,
       `t`.`updated_at` AS `updated_at`,
       `t`.`open_at` AS `open_at`,
       `t`.`closed_at` AS `closed_at`,
   	 `oh`.changed_at AS `stop_clock_start`,
       `t`.`estimated_stop_end` AS `estimated_stop_clock_end`,
       
       `t`.sub_status AS `status_detail`,
       `t`.`updated_by` AS `updated_by`,
       `tt`.`name` AS `ticket_type`,
       `af`.`name` AS `affecting_service`,
       `t`.`symptoms_details` AS `symptoms_details`,
       `t`.`product_type` AS `product_type`,
       `sv`.`name` AS `severity`,
       `ch`.`name` AS `channel_type`,
       `ti`.`login_temp` AS `login_temp`,
       `ti`.`sim_no` AS `sim_no`,
       `ti`.`sim_serial` AS `sim_serial`,
       `ti`.`sim_operator` AS `sim_operator`,
       `ti`.`sim_package` AS `sim_package`,
       `c`.`CusName` AS `customer_name`,
       `m`.Site_Address AS `site_address`,
       `ct`.`name` AS `channel_name`,
       `tp`.main_cause,
       `tp`.sub_cause,
       `tp`.issue_cause,
       `tp`.group_cause,
       `tp`.cause_name,
       `tp`.cause_name_en,
        `u`.`username` AS `created_by`
FROM `ticket_management`.`tickets` `t`
LEFT JOIN `ticket_management`.`ticket_extended` `te` on(`t`.`ticket_number` = `te`.`ticket_number`)
LEFT JOIN `ticket_management`.`ticket_interim` `ti` on(`t`.`ticket_number` = `ti`.`ticket_number`)
LEFT JOIN `ticket_management`.`users` `u` on(`t`.`username` = `u`.`username`)
LEFT JOIN `ticket_management`.`channel_types` `ct` on(`t`.`channel_type` = `ct`.`id`)
LEFT JOIN `ticket_management`.`ticket_cause_template` `tp` ON(`t`.`cause_detail_template_id`=`tp`.`id`) 
LEFT JOIN `ticket_management`.`ticket_type` `tt` ON(`t`.`ticket_type`=`tt`.`id`)
LEFT JOIN `ticket_management`.`affecting_service_types` `af` ON(`t`.`affecting_service`=`af`.`id`)
LEFT JOIN `ticket_management`.`severity_types` `sv` ON(`t`.`severity`=`sv`.`code`)
LEFT JOIN `ticket_management`.`channel_types` `ch` ON(`t`.`channel_type`=`ch`.`id`)
LEFT JOIN TmpOnHold `oh` ON(`t`.`ticket_number`=`oh`.`ticket_number`)
LEFT JOIN `KCS_DB`.`Main` `m` ON(`m`.Ref = `t`.`csno`) 
LEFT JOIN `KCS_DB`.`Connection_Type` `pt` ON(`m`.`Connection_Type` = `pt`.`Connection_ID`)
LEFT JOIN `KCS_DB`.`Customers` `c` on(`t`.`customer_number` = `c`.`CusCode`)
LEFT JOIN `KCS_DB`.`Project` `pj` ON (`m`.`Project_ID` = `pj`.`Project_ID`)
WHERE
t.is_import = 0
ORDER BY `t`.`ticket_number`

-- LIMIT 10
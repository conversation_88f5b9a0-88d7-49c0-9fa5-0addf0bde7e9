<?php
header('Content-Type: application/json');
require_once '../../config/database.php';
require_once '../../includes/authenCheck.php';

$action = $_POST['action'] ?? '';
$type = $_POST['type'] ?? '';
$title = $_POST['title'] ?? '';

$table_map = [
    'main' => 'ticket_main_cause',
    'sub' => 'ticket_sub_cause',
    'issue' => 'ticket_issue_cause',
    'group' => 'ticket_group_cause',
    'cause_template' => 'ticket_cause_template'
];

if (!isset($table_map[$type])) {
    echo json_encode(['success' => false, 'message' => 'Invalid type']);
    exit;
}

$table = $table_map[$type];

try {
    if ($action === 'add') {
        $stmt = $pdo->prepare("INSERT INTO $table (title) VALUES (?)");
        $stmt->execute([$title]);
        echo json_encode(['success' => true]);
    } elseif ($action === 'update') {
        $old_title = $_POST['old_title'] ?? '';
        $new_title = $_POST['new_title'] ?? '';
        $stmt = $pdo->prepare("UPDATE $table SET title = ? WHERE title = ?");
        $stmt->execute([$new_title, $old_title]);
        echo json_encode(['success' => true]);
    }elseif($action === 'edit_cause_template'){
        $template_id = $_POST['template_id'] ?? '';
        $cause_name = $_POST['cause_name'] ?? '';
        $cause_name_en = $_POST['cause_name_en'] ?? '';
        $stmt = $pdo->prepare("UPDATE $table SET cause_name = ?, cause_name_en = ? WHERE id = ?");
        $stmt->execute([$cause_name, $cause_name_en, $template_id]);
        echo json_encode(['success' => true]);
    }
    else {
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
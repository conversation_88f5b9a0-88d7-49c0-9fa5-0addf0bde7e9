-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: ************    Database: ticket_management
-- ------------------------------------------------------
-- Server version	5.5.5-10.5.27-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Temporary view structure for view `ticket_full_view`
--

DROP TABLE IF EXISTS `ticket_full_view`;
/*!50001 DROP VIEW IF EXISTS `ticket_full_view`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `ticket_full_view` AS SELECT 
 1 AS `id`,
 1 AS `ticket_number`,
 1 AS `username`,
 1 AS `customer_number`,
 1 AS `csno`,
 1 AS `issue_details`,
 1 AS `priority`,
 1 AS `status`,
 1 AS `assigned_team`,
 1 AS `assigned_worker`,
 1 AS `created_at`,
 1 AS `updated_at`,
 1 AS `closed_at`,
 1 AS `is_import`,
 1 AS `updated_by`,
 1 AS `ticket_type`,
 1 AS `affecting_service`,
 1 AS `symptoms_details`,
 1 AS `product_type`,
 1 AS `severity`,
 1 AS `channel_type`,
 1 AS `contact_person`,
 1 AS `location`,
 1 AS `effect`,
 1 AS `project_name`,
 1 AS `owner`,
 1 AS `noc_receiver`,
 1 AS `noc_closer`,
 1 AS `main_cause`,
 1 AS `sub_cause`,
 1 AS `group_cause1`,
 1 AS `group_cause2`,
 1 AS `cause_detail`,
 1 AS `cause_detail_eng`,
 1 AS `provider`,
 1 AS `province`,
 1 AS `downtime_hr`,
 1 AS `downtime_min`,
 1 AS `total_time`,
 1 AS `stop_clock`,
 1 AS `category`,
 1 AS `login_temp`,
 1 AS `sim_no`,
 1 AS `sim_serial`,
 1 AS `sim_operator`,
 1 AS `sim_package`,
 1 AS `customer_name`,
 1 AS `customer_address`,
 1 AS `team_name`,
 1 AS `created_by_username`,
 1 AS `channel_name`*/;
SET character_set_client = @saved_cs_client;

--
-- Final view structure for view `ticket_full_view`
--

/*!50001 DROP VIEW IF EXISTS `ticket_full_view`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`komsan`@`%` SQL SECURITY DEFINER */
/*!50001 VIEW `ticket_full_view` AS select `t`.`id` AS `id`,`t`.`ticket_number` AS `ticket_number`,`t`.`username` AS `username`,`t`.`customer_number` AS `customer_number`,`t`.`csno` AS `csno`,`t`.`issue_details` AS `issue_details`,`t`.`priority` AS `priority`,`t`.`status` AS `status`,`t`.`assigned_team` AS `assigned_team`,`t`.`assigned_worker` AS `assigned_worker`,`t`.`created_at` AS `created_at`,`t`.`updated_at` AS `updated_at`,`t`.`closed_at` AS `closed_at`,`t`.`is_import` AS `is_import`,`t`.`updated_by` AS `updated_by`,`t`.`ticket_type` AS `ticket_type`,`t`.`affecting_service` AS `affecting_service`,`t`.`symptoms_details` AS `symptoms_details`,`t`.`product_type` AS `product_type`,`t`.`severity` AS `severity`,`t`.`channel_type` AS `channel_type`,`te`.`contact_person` AS `contact_person`,`te`.`location` AS `location`,`te`.`effect` AS `effect`,`te`.`project_name` AS `project_name`,`te`.`owner` AS `owner`,`te`.`noc_receiver` AS `noc_receiver`,`te`.`noc_closer` AS `noc_closer`,`te`.`main_cause` AS `main_cause`,`te`.`sub_cause` AS `sub_cause`,`te`.`group_cause1` AS `group_cause1`,`te`.`group_cause2` AS `group_cause2`,`te`.`cause_detail` AS `cause_detail`,`te`.`cause_detail_eng` AS `cause_detail_eng`,`te`.`provider` AS `provider`,`te`.`province` AS `province`,`te`.`downtime_hr` AS `downtime_hr`,`te`.`downtime_min` AS `downtime_min`,`te`.`total_time` AS `total_time`,`te`.`stop_clock` AS `stop_clock`,`te`.`category` AS `category`,`ti`.`login_temp` AS `login_temp`,`ti`.`sim_no` AS `sim_no`,`ti`.`sim_serial` AS `sim_serial`,`ti`.`sim_operator` AS `sim_operator`,`ti`.`sim_package` AS `sim_package`,`c`.`CusName` AS `customer_name`,`c`.`CusAddress` AS `customer_address`,`tm`.`name` AS `team_name`,`u`.`username` AS `created_by_username`,`ct`.`name` AS `channel_name` from ((((((`ticket_management`.`tickets` `t` left join `ticket_management`.`ticket_extended` `te` on(`t`.`ticket_number` = `te`.`ticket_number`)) left join `ticket_management`.`ticket_interim` `ti` on(`t`.`ticket_number` = `ti`.`ticket_number`)) left join `KCS_DB`.`Customers` `c` on(`t`.`customer_number` = `c`.`CusCode`)) left join `ticket_management`.`teams` `tm` on(`t`.`assigned_team` = `tm`.`id`)) left join `ticket_management`.`users` `u` on(`t`.`username` = `u`.`username`)) left join `ticket_management`.`channel_types` `ct` on(`t`.`channel_type` = `ct`.`id`)) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-18 16:45:23

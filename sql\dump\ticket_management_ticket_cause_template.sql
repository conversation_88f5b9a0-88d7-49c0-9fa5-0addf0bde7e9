-- MySQL dump 10.13  Distrib 8.0.42, for Win64 (x86_64)
--
-- Host: ************    Database: ticket_management
-- ------------------------------------------------------
-- Server version	5.5.5-10.5.27-MariaDB

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `ticket_cause_template`
--

DROP TABLE IF EXISTS `ticket_cause_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `ticket_cause_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `main_cause` tinytext NOT NULL,
  `sub_cause` tinytext NOT NULL,
  `issue_cause` tinytext NOT NULL,
  `group_cause` tinytext NOT NULL,
  `cause_name` mediumtext NOT NULL,
  `cause_name_en` mediumtext NOT NULL,
  `status` int(11) NOT NULL DEFAULT 1 COMMENT '0-inactivem 1=active',
  `update_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `main_cause` (`main_cause`(255)),
  KEY `sub_cause` (`sub_cause`(255)),
  KEY `group_cause` (`group_cause`(255)),
  KEY `status` (`status`),
  KEY `issure_cause` (`issue_cause`(255)) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=89 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `ticket_cause_template`
--

LOCK TABLES `ticket_cause_template` WRITE;
/*!40000 ALTER TABLE `ticket_cause_template` DISABLE KEYS */;
INSERT INTO `ticket_cause_template` VALUES (1,'Customer','Application','Other','Customer Site Problem','ลูกค้าแจ้งใช้งานบาง Application ไม่ได้ ตรวจสอบพบ Link ปกติ แจ้งลูกค้าดำเนินการตรวจสอบแก้ไข','The issue was raised by a customer who can\' t use some of the applications or software. We have checked the internet connection was online normally.',1,'2025-07-14 04:24:33'),(2,'Customer','Full bandwidth','Other','Customer Site Problem','ลูกค้าแจ้งใช้งานได้ช้า ตรวจสอบพบลูกค้าใช้งาน Full BW ประสานงานลูกค้ารับทราบและวงจรสามารถใช้งานได้ปกติ','The issue was raised by a full of bandwidth. NOC has informed a customer about this issue already.',1,'2025-07-14 04:29:05'),(3,'Customer','Temporary closed','Other','Customer Maintenance/ยอมรับแผน','Monitor Link Down ลูกค้าปิดปรับปรุง Site ปลายทาง หลังลูกค้าเปิดอุปกรณ์ สามารถใช้งานได้ปกติ','The service was down due to the customer site undergoing renovation. The issue was resolved by completing the renovation and restoring the service.',1,'2025-07-14 04:30:03'),(4,'Customer','Electrical problems','Power','Power at Customer Site','Monitor Link Down ประสานงานลูกค้าปลายทางพบระบบไฟฟ้ามีปัญหา หลังลูกค้าดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','The service was down due to an electrical problem at the site. The issue was resolved by the customer fixing the electrical problem and restoring the service.',1,'2025-07-14 04:30:03'),(5,'Customer','Electrical switch off','Power','Power at Customer Site','Monitor Link Down ประสานงานลูกค้าปลายทางปิดปรับปรุงระบบไฟ หลังลูกค้าดำเนินการเรียบร้อย วงจรสามารถใช้งานได้ปกติ','The service was down due to the customer site undergoing electrical system maintenance. The issue was resolved by completing the maintenance and restoring the service.',1,'2025-07-14 04:30:03'),(6,'Customer','Turn off the device','Power','Power at Customer Site','Monitor Link Down ประสานงานลูกค้าปลายทางปิดอุปกรณ์ หลังลูกค้าเปิดอุปกรณ์ วงจรสามารถใช้งานได้ปกติ','The service was down due to the router being off at the customer site. The issue was resolved by turning on the router and restoring the service.',1,'2025-07-14 04:30:03'),(7,'Customer','Installation period','Hardware','Customer Maintenance/ยอมรับแผน','Monitor Link Down ตรวจสอบพบทีมงานดำเนินการติดตั้งอุปกรณ์และวงจรเช่า หลังดำเนินการเสร็จ วงจรสามารถใช้งานได้','The circuit was down due to the installation of equipment and leased circuits by the installation team. The issue was resolved by completing the installation and testing the circuit. The circuit was confirmed to be operational.',1,'2025-07-14 04:30:03'),(8,'Customer','Terminate','Other','Customer Site Problem','Monitor Link Down ตรวจสอบพบลูกค้าครบกำหนดสัญญาเช่า ยกเลิกวงจร','The circuit was down due to the customer reaching the end of the contract termination cycle. The issue was resolved by renewing the contract or terminating the service.',1,'2025-07-14 04:30:03'),(9,'Customer','Site copper cable problem','Hardware','Customer Site Problem','Monitor Link Down ตรวจสอบพบปัญหาสาย Copper ภายในลูกค้า หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','The circuit was down due to a problem with the copper cable inside the customer premises. The issue was resolved by troubleshooting and repairing the cable. The circuit was tested and confirmed to be operational.',1,'2025-07-14 04:30:03'),(10,'Customer','Site fiber optic cable problem','Hardware','Customer Site Problem','Monitor Link Down ตรวจสอบพบปัญหาสาย Optic fiber ภายในลูกค้า หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','The circuit was down due to a problem with the optic fiber cable inside the customer premises. The issue was resolved by troubleshooting and repairing the cable. The circuit was tested and confirmed to be operational.',1,'2025-07-14 04:30:03'),(11,'Customer','Incorrect connection of devices in the network','Hardware','Customer Site Problem','Monitor Link Down ตรวจสอบพบมีการต่ออุปกรณ์ใน Network ไม่ถูกต้อง หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','The circuit was down due to a device being improperly connected to the network. The issue was resolved by reconnecting the device correctly. The circuit was tested and confirmed to be operational.',1,'2025-07-14 04:30:03'),(12,'Customer','Network configuration','Software','Customer Defective Equipment','Monitor Link Down ประสานงานลูกค้าปลายทางมีการตั้งค่า network ไม่ถูกต้อง หลังลูกค้าดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','The circuit was down due to incorrect network settings at the end customer. The issue was resolved by coordinating with the customer to correct the network settings. The circuit was tested and confirmed to be operational.',1,'2025-07-14 04:30:03'),(13,'Customer','Overdue payment for media provider service','Other','Customer Site Problem','Monitor Link Down ลูกค้าค้างชำระบริการกับ Media Provider ประสานงานแก้ไขชั่วคราวพร้อมแจ้งลูกค้าดำเนินการชำระค่าบริการ วงจรคืนดี','The circuit was down due to the customer having overdue service fees with the Media Provider. The issue was resolved by coordinating with the Media Provider to temporarily restore the service and notifying the customer to pay the service fees. The reconciliation cycle was completed.',1,'2025-07-14 04:30:03'),(14,'Customer','Customer ask for help in using the service','Other','Customer Site Problem','ลูกค้าสอบถามข้อมูล และ/หรือ ขอให้ดำเนินการเพิ่มเติม','Customers request information and/or additional actions.',1,'2025-07-14 04:30:03'),(15,'Customer','IP Address blacklist','Software','Customer Site Problem','ลูกค้าแจ้งมีปัญหาการใช้งาน Public IP ตรวจสอบพบว่า IP ถูก Blacklist จาก Host ปลายทาง ให้คำแนะนำลูกค้าเพื่อแก้ไข','The customer experienced an issue with using Public IP, due to the IP being blacklisted by the destination host. The customer was advised to resolve the issue by removing the IP from the blacklist.',1,'2025-07-14 04:30:03'),(16,'Other','Unidentified','Other','Provider Configuration','Monitor Link Down วงจรคืนดีระหว่างตรวจสอบ ยังไม่มีการแก้ไข','The circuit was reconciled by the Monitor Link Down during the inspection.',1,'2025-07-14 04:30:03'),(17,'Media Provider','Block Service Port','Software','Provider Configuration','ลูกค้าแจ้งใช้งาน Application ไม่ได้ เหตุเกิดจาก Media Provider มีการ Block service port หลังประสานงานแก้ไข ลูกค้าสามารถใช้งานได้ปกติ','The application experienced a temporary outage due to the Media Provider blocking the service port. The issue was resolved by coordinating with the Media Provider to unblock the port.',1,'2025-07-14 04:30:03'),(18,'Media Provider','NAT44','Network','Provider Configuration','ลูกค้าแจ้งใช้งาน Application ไม่ได้ เหตุเกิดจาก Media Provider มีการทำ NAT44 หลังประสานงานแก้ไข ลูกค้าสามารถใช้งานได้ปกติ','The customer faced an issue with the application due to the Media Provider implementing NAT44. The problem was fixed by coordinating with the Media Provider to revert the NAT44 configuration. The customer confirmed that the application was working as expected.',1,'2025-07-14 04:30:03'),(19,'Media Provider','Internet link problem','Network','Provider Defective Equipment','ลูกค้าแจ้งใช้งาน Internet ไม่ได้ เหตุเกิดจาก Link International / Domestic ของ Media Provoider มีปัญหา หลังดำเนินการแก้ไข สามารถใช้งานได้ปกติ','The customer experienced an Internet outage due to a problem with the Media Provider\'s International/Domestic Link. The issue was resolved by troubleshooting the Link and restoring the connectivity. The customer verified that the Internet was accessible again.',1,'2025-07-14 04:30:03'),(20,'Media Provider','Network problem','Network','Provider Defective Equipment','Monitor Link Down ประสานงาน Provider พบโครงข่ายมีปัญหา หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ตามปกติ','The service was disrupted by the media operator’s signal problem. After resetting the signal, the service was restored.',1,'2025-07-14 04:30:03'),(21,'Media Provider','Site equipment fail','Hardware','Provider Defective Equipment','Monitor Link Down สาเหตุจากอุปกรณ์ของ Media Provider ที่ Site ลูกค้าเสีย หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','The circuit was down due to a faulty Media Provider equipment at the customer premises. The issue was fixed by replacing the equipment and restoring the link. The circuit was tested and confirmed to be operational.',1,'2025-07-14 04:30:03'),(22,'Media Provider','Site Equipment Hang','Hardware','Provider Defective Equipment','Monitor Link Down สาเหตุจากอุปกรณ์ของ Media Provider ที่ Site ลูกค้า hang หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','The circuit was down due to a Media Provider equipment hang at the customer site. The issue was fixed by rebooting the equipment and re-establishing the link. The circuit was tested and confirmed to be operational.',1,'2025-07-14 04:30:03'),(23,'Media Provider','Copper cable problem','Network','Provider Defective Equipment','Monitor Link Down ประสานงาน Provider ตรวจสอบพบปัญหาจากสายทองแดง หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ตามปกติ','The circuit was down due to a problem with the copper cables. The issue was resolved by coordinating with the Provider to troubleshoot and repair the cables. The circuit was tested and confirmed to be operational.',1,'2025-07-14 04:30:03'),(24,'Media Provider','Optic fiber cable problem','Network','Provider Defective Equipment','Monitor Link Down ประสานงาน Provider ตรวจสอบพบปัญหาจากสาย Optic fiber หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ตามปกติ','The circuit was down due to a problem with the optic fiber cable from ISP. The issue was resolved by coordinating with the Provider to troubleshoot and repair the cable. The circuit was tested and confirmed to be operational.',1,'2025-07-14 04:30:03'),(25,'Media Provider','Sim Problem','Hardware','Provider Configuration','Monitor Link Down ประสานงาน Provider พบซิมการ์ดปัญหา, ชำรุด หรือเสีย หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ตามปกติ','The circuit was down due to a SIM card problem, such as damage or malfunction. The issue was resolved by coordinating with the Provider to replace or fix the SIM card. The circuit was tested and confirmed to be operational.',1,'2025-07-14 04:30:03'),(26,'Media Provider','Sim Terminate','Hardware','Other','Monitor Link Down ประสานงาน Provider พบซิมการ์ดถูกยกเลิก หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ตามปกติ','The Circuit was down due to a SIM card problem, such as damage or malfunction. The issue was resolved by coordinating with the Provider to replace or fix the SIM card. The circuit was tested and confirmed to be operational.',1,'2025-07-14 04:30:03'),(27,'Ji-Net','Exceed concurrent connection','Software','Configuration','Monitor Link Down ตรวจสอบพบ Session Connect ค้าง หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ตามปกติ','Monitor Link Down reports that Session Connect was frozen and required corrective action. The circuit is now functional again.',1,'2025-07-14 04:30:03'),(28,'Ji-Net','Site Router Fail','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ router เสีย หลังดำเนินการแก้ไขโดยการเปลี่ยนอุปกรณ์ใหม่ วงจรสามารถใช้งานได้ปกติ','The router was defective and needed replacement. We dispatched a technician to install a new router, and the service was restored.',1,'2025-07-14 04:30:03'),(29,'Ji-Net','Site Router Hang','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ router hang หลังดำเนินการ Reboot อุปกรณ์ใหม่ วงจรสามารถใช้งานได้ปกติ','The router was hanging and required rebooting. We contacted the customer site to perform the reboot, and the service was restored.',1,'2025-07-14 04:30:03'),(30,'Ji-Net','Site switch/hub fail','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ switch/hub เสีย หลังดำเนินการแก้ไขโดยการเปลี่ยนอุปกรณ์ใหม่ วงจรสามารถใช้งานได้ปกติ','The switch or hub was faulty and needed replacement. We dispatched a technician to install a new device, and the service was restored.',1,'2025-07-14 04:30:03'),(31,'Ji-Net','Site switch/hub hang','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ switch/hub hang หลังดำเนินการ Reboot อุปกรณ์ใหม่ วงจรสามารถใช้งานได้ปกติ','The switch or hub was hanging and required rebooting. We contacted the customer site to perform the reboot, and the service was restored.',1,'2025-07-14 04:30:03'),(32,'Ji-Net','Site aircard fail','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ aircard เสีย หลังดำเนินการแก้ไขโดยการเปลี่ยนอุปกรณ์ใหม่ วงจรสามารถใช้งานได้ปกติ','The aircard was defective and needed replacement. We dispatched a technician to install a new aircard, and the service was restored.',1,'2025-07-14 04:30:03'),(33,'Ji-Net','Site Aircard Hang','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ aircard hang หลังดำเนินการ Reboot อุปกรณ์ใหม่ วงจรสามารถใช้งานได้ปกติ','The aircard was hanging and required rebooting. We contacted the customer site to perform the reboot, and the service was restored.',1,'2025-07-14 04:30:03'),(34,'Ji-Net','Site Access Point Fail','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ access point เสีย หลังดำเนินการแก้ไขโดยการเปลี่ยนอุปกรณ์ใหม่ วงจรสามารถใช้งานได้ปกติ','The access point was faulty and needed replacement. We dispatched a technician to install a new access point, and the service was restored.',1,'2025-07-14 04:30:03'),(35,'Ji-Net','Site Access Point Hang','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ access point hang หลังดำเนินการ Reboot อุปกรณ์ใหม่ วงจรสามารถใช้งานได้ปกติ','The access point was hanging and required rebooting. We contacted the customer site to perform the reboot, and the service was restored.',1,'2025-07-14 04:30:03'),(36,'Ji-Net','Site firewall fail','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ firewall เสีย หลังดำเนินการแก้ไขโดยการเปลี่ยนอุปกรณ์ใหม่ วงจรสามารถใช้งานได้ปกติ','The firewall was defective and needed replacement. We dispatched a technician to install a new firewall, and the service was restored.',1,'2025-07-14 04:30:03'),(37,'Ji-Net','Site Firewall Hang','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ firewall hang หลังดำเนินการ Reboot อุปกรณ์ใหม่ วงจรสามารถใช้งานได้ปกติ','The firewall was hanging and required rebooting. We contacted the customer site to perform the reboot, and the service was restored.',1,'2025-07-14 04:30:03'),(38,'Ji-Net','Site ups fail','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ ups เสีย หลังดำเนินการแก้ไขโดยการเปลี่ยนอุปกรณ์ใหม่ วงจรสามารถใช้งานได้ปกติ','The ups was faulty and needed replacement. We dispatched a technician to install a new ups, and the service was restored.',1,'2025-07-14 04:30:03'),(39,'Ji-Net','Site ups hang','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ ups hang หลังดำเนินการ Reboot อุปกรณ์ใหม่ วงจรสามารถใช้งานได้ปกติ','The ups was hanging and required rebooting. We contacted the customer site to perform the reboot, and the service was restored.',1,'2025-07-14 04:30:03'),(40,'Ji-Net','Site equipment configuration problem','Software','Configuration','Monitor Link Down ตรวจสอบพบปัญหา configuration อุปกรณ์ไม่ถูกต้อง หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','Monitor Link Down reports an invalid device configuration issue. After correcting the configuration, the circuit is functional again.',1,'2025-07-14 04:30:03'),(41,'Ji-Net','Radius server problem','Software','Configuration','Monitor Link Down ตรวจสอบพบปัญหา Radius server หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','Monitor Link Down identifies a Radius server issue. After resolving it, the circuit is functional again.',1,'2025-07-14 04:30:03'),(42,'Ji-Net','Authentication server problem','Software','Configuration','Monitor Link Down ตรวจสอบพบปัญหา Authentication server หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','Monitor Link Down reports an authentication server issue. After editing the server settings, the circuit is functional again.',1,'2025-07-14 04:30:03'),(43,'Ji-Net','E-mail server problem','Software','Configuration','ลูกค้าแจ้ง ใช้งาน E-mail ไม่ได้ ตรวจสอบพบปัญหา E-mail server หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','The customer reported an issue with the E-mail service. The E-mail server was malfunctioning and needed fixing. After resolving the server issue, the circuit is functional again.',1,'2025-07-14 04:30:03'),(44,'Ji-Net','Internet link problem','Hardware','Defective Equipment','ลูกค้าแจ้งใช้งาน Internet ไม่ได้ เหตุเกิดจาก Link International / Domestic ของ Ji-net มีปัญหา หลังดำเนินการแก้ไข สามารถใช้งานได้ปกติ','The customer reported an issue with the Internet service due to Ji-net’s International / Domestic Link problem. After resolving the link problem, the service is functional again',1,'2025-07-14 04:30:03'),(45,'Ji-Net','User profile problem','Software','Configuration','ลูกค้าแจ้งใช้งาน Internet ไม่ได้ เหตุเกิดจาก User profile มีปัญหา หลังดำเนินการแก้ไข สามารถใช้งานได้ปกติ','The customer reported an issue with the Internet service due to a user profile problem. After correcting the user profile, the service is functional again.',1,'2025-07-14 04:30:03'),(46,'Ji-Net','Monitor System','Software','Configuration','ปัญหาระบบ Monitor หลังดำเนินการแก้ไข สามารถ Monitor วงจรได้ตามปกติ','The monitoring system had an issue. After resolving it, the circuit can be monitored normally.',1,'2025-07-14 04:30:03'),(47,'Ji-Net','Core network problem','Network','Defective Equipment','Monitor Link Down ตรวจสอบพบปัญหา Core network หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','Monitor Link Down reports a Core network issue. After resolving the issue, the circuit is functional again.',1,'2025-07-14 04:30:03'),(48,'Ji-Net','DNS server problem','Software','Configuration','ลูกค้าแจ้งใช้งาน Internet ไม่ได้ เหตุเกิดจาก DNS ของ Ji-net มีปัญหา หลังดำเนินการแก้ไข สามารถใช้งานได้ปกติ','The customer experienced an Internet outage due to a DNS issue with Ji-net. The issue has been resolved and the Internet service is now functioning normally.',1,'2025-07-14 04:30:03'),(49,'Hosting Provider','Hosting Provider problem','Software','Software','ลูกค้าแจ้งมีปัญหาใช้งาน Hosting ประสานงานทาง Hosting Provider แก้ไข หลังดำเนินการแก้ไข สามารถใช้งานได้ปกติ','The customer encountered a hosting issue, which was resolved by coordinating with the Hosting Provider. The hosting service is now working normally.',1,'2025-07-14 04:30:03'),(50,'Ji-Net','Site Adapter fail','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ Adapter Router เสีย หลังดำเนินการเปลี่ยนอุปกรณ์ใหม่วงจรสามารถใช้งานได้ปกติ','The circuit was disrupted by a faulty Adapter Router, which was detected by the Monitor Link Down. The device has been replaced with a new one and the circuit is now operational.',1,'2025-07-14 04:30:03'),(51,'Customer','Lan Problem','Hardware','Customer Defective Equipment','Monitor Link Down ตรวจสอบพบ สาย Lan ภายใน Site ลูกค้า หลุด/หลวม/ชำรุด หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','The circuit was down due to a loose or damaged Lan cable inside the customer site. The issue was resolved by fixing or replacing the cable. The circuit was tested and confirmed to be operational.',1,'2025-07-14 04:30:03'),(52,'Customer','Power Outage at Customer Site','Power','Power at Customer Site','Monitor Link Down ตรวจสอบพบไฟฟ้าดับที่สาขาปลายทางของลูกค้า หลังการไฟฟ้าจ่ายไฟฟ้า วงจรสามารถใช้งานได้ปกติ','The service was down due to a power outage at the customer site. The issue was resolved by the electricity authority completing their work and restoring the power. The service was tested and confirmed to be operational.',1,'2025-07-14 04:30:03'),(53,'Customer','Plugs Disconnect','Power','Customer Site Problem','Monitor Link Down ตรวจสอบพบสาเหตุเกิดจากปลั๊ก อุปกรณ์ หลุด หลวม แจ้งลูกค้าขยับปลั๊กใหม่ วงจรสามารถใช้งานได้ปกติ','The service was down due to plugs being disconnected. The issue was resolved by our technician contacting the customer site and plugging in the router. The service was tested and confirmed to be operational.',1,'2025-07-14 04:30:03'),(54,'Ji-Net','Natural Disaster','Other','Other','Monitor Link Down ตรวจสอบพบสาเหตุุเกิดจากภัยธรรมชาติ หลังสถานการณ์คลี่คลาย วงจรสามารใช้งานได้ปกติ','The circuit was affected by a natural disaster, which was detected by the Monitor Link Down. The situation has been resolved and the circuit is now functional.',1,'2025-07-14 04:30:03'),(55,'Customer','Renovate','Other','Customer Site Problem','Monitor Link Down ตรวจสอบพบ ลูกค้าปิดอุปกรณ์เนื่องจากปรับปรุงสถานที่ ขอปิด Ticket แล้วเปิดใหม่เป็น (Follow UP)','The circuit was down due to the customer turning off the device for site renovation. The customer requested to close the ticket and reopen it as a follow-up.',1,'2025-07-14 04:30:03'),(56,'Customer','IN House Wiring','Other','Customer Site Problem','Monitor Link Down ตรวจสอบพบสายภายในลูกค้ามีปัญหา ไม่ทราบระยะเวลากำหนดการแก้ไข ขอปิด Ticket แล้วเปิดใหม่เป็น (Follow UP)','Monitor Link Down indicates an issue with the customer’s internal line. The editing timeframe is not specified.',1,'2025-07-14 04:30:03'),(57,'Customer','Change Solution','Other','Customer Site Problem','Monitor Link Down ตรวจสอบพบคู่สายภายในมีปัญหา ไม่สามารถแก้ไขได้ ประสานงาน PM/Sale พิจารณา Change Solution ขอปิด Ticket แล้วเปิดใหม่เป็น (Follow UP)','Monitor Link Down identifies an internal cable issue. It is not resolvable. Please contact PM/Sale and evaluate alternative solutions.',1,'2025-07-14 04:30:03'),(58,'Ji-Net','Ticket System','Other','Other','ข้อมูลผิดพลาด ปิดเคส ไม่นำมาทำรายงาน','ข้อมูลผิดพลาด ปิดเคส ไม่นำมาทำรายงาน',1,'2025-07-14 04:30:03'),(59,'Ji-Net','Equipment problem','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ Adapter Router เสีย เนื่องจากมดทำรัง หลังดำเนินการเปลี่ยนอุปกรณ์ใหม่วงจรสามารถใช้งานได้ปกติ','Monitor Link Down the adapter router was found broken due to the nesting of ants. After replacing the equipment with new ones, the circuit was back online.',1,'2025-07-14 04:30:03'),(60,'Ji-Net','Equipment problem','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ Router เสีย เนื่องจากมดทำรัง หลังดำเนินการเปลี่ยนอุปกรณ์ใหม่วงจรสามารถใช้งานได้ปกติ','Monitor Link Down the router was found broken due to the nesting of ants. After replacing the equipment with new ones, the circuit was back online.',1,'2025-07-14 04:30:03'),(61,'Ji-Net','Equipment problem','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ Switch เสีย เนื่องจากมดทำรัง หลังดำเนินการเปลี่ยนอุปกรณ์ใหม่วงจรสามารถใช้งานได้ปกติ','Monitor Link Down the switch was found broken due to the nesting of ants. After replacing the equipment with new ones, the circuit was back online.',1,'2025-07-14 04:30:03'),(62,'1-To-All','Maintanance network','Network','Maintenance','Monitor Link Down ตรวจสอบพบมีการดำเนินงาน Maintenance โครงข่าย หลังดำเนินการเรียบร้อย วงจรสามารถใช้งานได้ตามปกติ','Network monitoring system detected a link down. Upon investigation, it was discovered that network maintenance was being conducted. Following the completion of maintenance, the network is now fully operational.',1,'2025-07-14 04:30:03'),(63,'Customer','Customer has an outstanding balance','Customer','Customer Site Problem','ลูกค้าค้างชำระค่าบริการ หลังลูกค้าดำเนินการชำระค่าบริการ วงจรคืนดีปกติ','Customer had an outstanding payment. Upon payment, the service was back online.',1,'2025-07-14 04:30:03'),(64,'1-To-All','Equipment problem','Hardware','Defective Equipment','ตรวจสอบอุปกรณ์ VOICE GW มีปัญหา หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','Investigation revealed an issue with the VOICE GW device. Upon troubleshooting and resolving the problem, the network is now functioning normally.',1,'2025-07-14 04:30:03'),(65,'1-To-All','Service Problem','Service','Service Outage','ลูกค้าแจ้งปัญหาการใช้งาน หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','The customer reported a usage issue. After troubleshooting, the network is now functioning normally.',1,'2025-07-14 04:30:03'),(66,'Third-Party','Service Problem','Service','Service Outage','ลูกค้าแจ้งปัญหาการใช้งาน ประสานงาน Partner ตรวจสอบ หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','The customer reported a usage issue. We coordinated with our partner to investigate the problem. After troubleshooting, the network is now functioning normally.',1,'2025-07-14 04:30:03'),(67,'Customer','Equipment problem','Customer','Customer Defective Equipment','Monitor Link/Service Down ประสานงานลูกค้าปลายทางแจ้งอุปกรณ์ลูกค้ามีปัญหา หลังลูกค้าดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','A network monitoring system detected a link or service outage. We contacted customer to inform them that their equipment was experiencing an issue. After the customer resolved the equipment problem, the network connection was restored.',1,'2025-07-14 04:30:03'),(68,'Media Provider','Service Port Error','Network','Provider Configuration','Internet Up/Down ประสานงาน Provider ตรวจสอบ Service Port Error หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ตามปกติ','Internet Up/Down coordinates with Provider to check Service Port Error. After correcting the problem, the circuit can be used normally.',1,'2025-07-14 04:30:03'),(69,'1-To-All','Site equipment configuration problem','Software','Configuration','Monitor Link Down ตรวจสอบพบ Config Switch ผิด หลังดำเนินการ Config ใหม่อุปกรณ์ใหม่วงจรสามารถใช้งานได้ปกติ','Monitor Link Down, Found incorrect configuration of the Switch. After reconfiguring, the circuit returned to normal operation.',1,'2025-07-14 04:30:03'),(70,'1-To-All','Equipment problem','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ Adapter Switch เสีย หลังดำเนินการเปลี่ยนอุปกรณ์ใหม่วงจรสามารถใช้งานได้ปกติ','Monitor Link Down Found the Adapter of the Switch was broken. After replacing the device the circuit returned to normal operation.',1,'2025-07-14 04:30:03'),(71,'Customer','Equipment problem','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ Media Converter เสีย ประสานงานลูกค้าเปลี่ยนอุปกรณ์ใหม่วงจรสามารถใช้งานได้ปกติ','Monitor link down. Found faulty media converter. Coordinated with the customer to replace the device. The circuit is now operating normally.',1,'2025-07-14 04:30:03'),(72,'1-To-All','Equipment problem','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ Adapter Switch เสีย เนื่องจากมดทำรัง หลังดำเนินการเปลี่ยนอุปกรณ์ใหม่วงจรสามารถใช้งานได้ปกติ','Monitor Link Down Found the Adapter of the Switch was broken due to ants nesting. After replacing the device the circuit returned to normal operation.',1,'2025-07-14 04:30:03'),(73,'1-To-All','Equipment problem','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ access point เสีย เนื่องจากมดทำรัง หลังดำเนินการแก้ไขโดยการเปลี่ยนอุปกรณ์ใหม่ วงจรสามารถใช้งานได้ปกติ','Monitor Link Down: An access point failure was detected due to an ant nest. After replacing the equipment, the circuit is now functioning normally.',1,'2025-07-14 04:30:03'),(74,'1-To-All','Site Adapter fail','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ Adaptor access point เสีย หลังดำเนินการแก้ไขโดยการเปลี่ยนอุปกรณ์ใหม่ วงจรสามารถใช้งานได้ปกติ','Monitor Link Down: It was found that the access point adaptor had failed. After replacing the equipment, the circuit is now functioning normally.',1,'2025-07-14 04:30:03'),(75,'1-To-All','Summarize the information for Cybertron.','Software','Security Incident','Cybertron แจ้ง Security Incident มาให้ทำการสรุปผลและแจ้งกลับ','Cybertron has reported a security incident and requested that the findings be summarized and reported back.',1,'2025-07-14 04:30:03'),(76,'1-To-All','Ticket System','Software','Software','ทดสอบระบบ Alert Ticket Management','Testing Ticket System Alerts',1,'2025-07-14 04:30:03'),(77,'1-To-All','Internet Customer Site','Hardware','Configuration','Internet Up/Down ประสานงาน 2nd (SQM) ตรวจสอบ และดำเนินการแก้ไข Policy Next Gen Firewall วงจรลูกค้าจึงกลับมาใช้งานได้ตามปกติ','Coordinated with 2nd level support (SQM) to investigate Internet up/down issue and resolved it by adjusting the next gen firewall policy. The customer\'s circuit is now back to normal operation.',1,'2025-07-14 04:30:03'),(78,'1-To-All','The site router is misconfigured by a Meraki Cloud issue.','Software','Configuration','Monitor Link Down ตรวจสอบพบปัญหา configuration อุปกรณ์ไม่ถูกต้อง หลังดำเนินการแก้ไข วงจรสามารถใช้งานได้ปกติ','Monitor Link Down reports an invalid device configuration issue. After correcting the configuration, the circuit is functional again.',1,'2025-07-14 04:30:03'),(79,'1-To-All','Faulty UTP uplink cable to ISP gateway.','Hardware','Defective Equipment','ตรวจสอบพบสาย LAN UTP Uplink ไป อุปกรณ์ ISP Gateway เสีย หลังดำเนินการเปลี่ยนสาย Uplink ใหม่วงจร สามารถใช้งานได้ปกติ','Following detection of a faulty UTP cable between the router and ISP gateway, the cable was replaced, restoring stable uplink connectivity.',1,'2025-07-14 04:30:03'),(80,'1-To-All','Equipment problem','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ Router เสีย เนื่องจากปัญหาระบบไฟฟ้าของลูกค้า หลังดำเนินการเปลี่ยนอุปกรณ์ใหม่วงจรสามารถใช้งานได้ปกติ','The monitor detected a link down issue. Investigation found that the router was faulty due to the customer\'s electrical system problem. After replacing the device, the circuit resumed normal operation.',1,'2025-07-14 04:30:03'),(81,'1-To-All','Equipment problem','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ Switch เสีย เนื่องจากปัญหาระบบไฟฟ้าของลูกค้า หลังดำเนินการเปลี่ยนอุปกรณ์ใหม่วงจรสามารถใช้งานได้ปกติ','Link down was detected. The switch was found to be faulty due to a power issue at the customer site. After replacement, the circuit is now functioning normally.',1,'2025-07-14 04:30:03'),(82,'1-To-All','Equipment problem','Hardware','Defective Equipment','Monitor Link Down ตรวจสอบพบ Access Point เสีย เนื่องจากปัญหาระบบไฟฟ้าของลูกค้า หลังดำเนินการเปลี่ยนอุปกรณ์ใหม่วงจรสามารถใช้งานได้ปกติ','Link down detected. The access point failed due to a power issue at the customer site. After replacement, the circuit is now functioning normally.',1,'2025-07-14 04:30:03'),(88,'1-To-All','Full bandwidth','Network','Configuration','please increase the bandwidth','please increase the bandwidth',1,'2025-07-15 05:33:56');
/*!40000 ALTER TABLE `ticket_cause_template` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-18 16:45:14
